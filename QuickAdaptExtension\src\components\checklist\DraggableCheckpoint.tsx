import React, { useReducer, useState,useEffect, use<PERSON>ontext, useRef } from "react";
import { <PERSON>, Typo<PERSON>, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from "../../store/drawerStore";
import { HOTSPOT_DEFAULT_VALUE } from "../../store/drawerStore";
import {
    InfoFilled,
    QuestionFill,
    Reselect,
      Solid,
      editicon,
      deleteicon,
      deletestep,
      editpricol
  } from "../../assets/icons/icons";

// Function to modify the color of an SVG icon
const modifySVGColor = (base64SVG: any, color: any) => {
	if (!base64SVG) {
		return "";
	}

	try {
		// Check if the string is a valid base64 SVG
		if (!base64SVG.includes("data:image/svg+xml;base64,")) {
			return base64SVG; // Return the original if it's not an SVG
		}

		const decodedSVG = atob(base64SVG.split(",")[1]);

		// Check if this is primarily a stroke-based or fill-based icon
		const hasStroke = decodedSVG.includes('stroke="');
		const hasColoredFill = /fill="(?!none)[^"]+"/g.test(decodedSVG);

		let modifiedSVG = decodedSVG;

		if (hasStroke && !hasColoredFill) {
			// This is a stroke-based icon (like chkicn2-6) - only change stroke color
			modifiedSVG = modifiedSVG.replace(/stroke="[^"]+"/g, `stroke="${color}"`);
		} else if (hasColoredFill) {
			// This is a fill-based icon (like chkicn1) - only change fill color
			modifiedSVG = modifiedSVG.replace(/fill="(?!none)[^"]+"/g, `fill="${color}"`);
		} else {
			// No existing fill or stroke, add fill to make it visible
			modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill="${color}"`);
			modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill="${color}"`);
		}

		const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;
		return modifiedBase64;
	} catch (error) {
		console.error("Error modifying SVG color:", error);
		return base64SVG; // Return the original if there's an error
	}
};
  const DraggableCheckpoint = ({
    checkpoint,
    index,
    handleEditClick,
    handleDeleteClick,
    handleDragStart,
    handleDragOver,
    handleDrop,
    isDragging,
}: any) => {
    return (
			<Box
				key={index}
				draggable
				onDragStart={() => handleDragStart(index)}
				onDragOver={handleDragOver}
				onDrop={() => handleDrop(index)}
				className="qadpt-control-box"
				sx={{
					backgroundColor: "#E5DADA !important",
					height: "auto !important",
					cursor: "grab",
					padding: "0 !important",
					"&:hover": { backgroundColor: "#D9CACA" },
				}}
			>
				<div
					style={{
						width: "100%",
						textOverflow: "ellipsis",
						overflow: "hidden",
						display: "block !important",
						whiteSpace: "nowrap",
						textAlign: "left",
						fontWeight: "bold",
					}}
					className="qadpt-drag"
				>
					{/* Title */}
					<div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
						{/* {checkpoint.icon && (
							<div
								style={{
									width: "24px",
									height: "24px",
									borderRadius: "50%",
									backgroundColor:
										useDrawerStore.getState().checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || "#333",
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
								}}
							>
								<img
									src={checkpoint.icon}
									alt="icon"
									style={{ width: "14px", height: "14px", filter: "brightness(0) invert(1)" }}
								/>
							</div>
						)} */}
						<Typography
							className="qadpt-control-label"
							sx={{
								textOverflow: "ellipsis",
								overflow: "hidden",
								display: "block !important",
								whiteSpace: "nowrap",
								fontWeight: "bold",
							}}
						>
							{checkpoint.title}
						</Typography>
						<Tooltip
							arrow
							title="Edit"
						>
							<IconButton
								onClick={() => handleEditClick(checkpoint?.id)}
								sx={{ padding: "5px !important" }}
							>
								<span
									dangerouslySetInnerHTML={{ __html: editpricol }}
									style={{ zoom: 1 }}
								/>
							</IconButton>
						</Tooltip>

						{/* Delete Button */}
						<Tooltip
							arrow
							title="Delete"
						>
							<IconButton
								onClick={() => handleDeleteClick(checkpoint?.interaction)}
								sx={{ padding: "5px !important" }}
							>
								<span
									dangerouslySetInnerHTML={{ __html: deletestep }}
									style={{ zoom: 1 }}
								/>
							</IconButton>
						</Tooltip>
					</div>

					{/* Description */}
					{checkpoint.description && (
						<Typography
							className="qadpt-desdrag"
							sx={{
								padding: "0 8px 8px 8px",
								textOverflow: "ellipsis",
								overflow: "hidden",
								display: "block !important",
								whiteSpace: "nowrap",
							}}
						>
							{checkpoint.description}
						</Typography>
					)}
				</div>

				{/* Edit Button */}
			</Box>
		);
};

export default DraggableCheckpoint;

