@import "/src/assets/icon.css";

:root {
	--font-family: Poppins, Proxima Nova, arial, serif;
    --primarycolor: #5F9EA0;
    --ext-background: #F6EEEE;
    --border-color: #ccc;
    --white-color: #fff;
    --back-light-color:#EAE2E2;
    --button-border-radius: 12px !important
}
*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon) {
	font-family: var(--font-family) !important;
}
body {
	margin: 0;
	font-family: var(--font-family) !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

@font-face {
	font-family: "Gotham Pro";
	font-style: normal;
	/* src: local("Gotham Pro"), local("Gotham Pro"), url("../../../assets/fonts/GothamPro.woff2") format("woff2"),
		url("../../../assets/fonts/GothamPro.woff") format("woff"); */
}

@font-face {
	font-family: "Proxima Nova";
	font-style: normal;
	src: local("Proxima Nova"), local("ProximaNova-Regular"),
		url("../../../assets/fonts/ProximaNova-Regular.woff2") format("woff2"),
		url("../../../assets/fonts/ProximaNova-Regular.woff") format("woff");
}

@font-face {
	font-family: "qadapt-icons";
	src: url("../../../assets/fonts/qadapt-icons.eot?qmcsfb");
	src: url("../../../assets/fonts/qadapt-icons.eot?qmcsfb#iefix") format("embedded-opentype"),
		url("../../../assets/fonts/qadapt-icons.ttf?qmcsfb") format("truetype"),
		url("../../../assets/fonts/qadapt-icons.woff?qmcsfb") format("woff"),
		url("../../../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons") format("svg");
	font-weight: normal;
	font-style: normal;
	font-display: block;
}
/* Side menu */
 .side-menu .menu-list {
	 list-style: none;
	 padding: 0;
	 margin: 0;
}
 .side-menu .menu-list .menu-item {
  padding: 6px 12px 12px 12px;
  margin: 10px 0;
  border-radius: 12px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border: 1px solid #e4e4e4;
  list-style-type: none !important;
}
.side-menu .menu-list .menu-item:hover {border: 1px solid var(--primarycolor) !important;}

 /* .side-menu .menu-list .menu-item .menu-content {
  display: flex;
  width: 100%;
  flex-direction: column;
} */
 .side-menu .menu-list .menu-item .menu-text {
	 /* display: flex;
	 flex-direction: column; */
   font-size: 12px !important;
   line-height: initial !important;
}
 .side-menu .menu-list .menu-item .menu-text .menu-title {
	 font-size: 16px !important;
	 font-weight: 600;
	 margin-bottom: 4px;
	 text-align: left;
   color: #000 !important;
}
 .side-menu .menu-list .menu-item .menu-text .menu-description {
	 font-size: 12px;
	 color: #9c9c9c;
	 text-align: left;
   line-height: initial !important;
}
 .side-menu .menu-list .menu-item .icons {
	 display: flex;
	 align-items: center;
   margin-right: 10px;
}

  .menu-item.disabled {
    opacity: 0.5;
    pointer-events: none;
}
.menu-item.active {
 background: rgba(95, 158, 160, 0.1);
  border: 1px solid var(--primarycolor) !important;
}

.qadpt-gud-menupopup {
  margin-left: 300px;
  height: calc(100vh + 13px);
  border-radius: 30px;
  z-index: 99999 !important;
}
.qadpt-gud-menupopup .MuiPaper-root{
  border-radius: 25px;
}
.qadpt-gud-menupopup-content {
  background: var(--ext-background);
  padding-bottom: 0 !important;
  z-index: 9999;
}
.qadpt-head {
  margin: 0 -20px;
  padding: 12px 20px 5px 20px;
}
.qadpt-titsection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
 /* Ensure titsection takes up the full width */
}
.qadpt-extsearch {
  background: var(--white-color);
  border-radius: 10px;
  width: 70%;
}
.qadpt-extsearch .MuiOutlinedInput-root {
  height: 40px;
  border-radius: 10px;
  border: 1px solid #a8a8a8;
}
.qadpt-extsearch .MuiOutlinedInput-input {
  /* height: inherit !important; */
  border: 0 !important;
  border-radius: inherit !important;
  /* line-height: inherit !important; */
  font-size: initial !important;
}
.qadpt-extsearch .MuiOutlinedInput-notchedOutline {
  border-radius: 10px !important;
  border: none !important;
}
.qadpt-right-part {
  display: flex;
  /* justify-content: flex-end;

  width: 25%; */
}
.qadpt-subhead{
display: flex;
align-items: center;
}
.qadpt-subhead .title{
font-size: 18px !important;
    font-weight: 600 !important;
    width: 100%;}
.qadpt-memberButton {
  background-color: var(--primarycolor) !important;
  border: none !important;
  border-radius: var(--button-border-radius) !important;
  color: var(--white-color) !important;
  cursor: pointer !important;
  font-size: 14px !important;
  padding: 10px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center;
}
.qadpt-memberButton i {
  margin-right: 10px;
}
.qadpt-memberButton span {
  font-size: 14px;
}

.qadpt-tabs-container {
  border-bottom: 1px solid #ccc;
  margin-bottom: 10px;


}
.qadpt-memberButton.qadpt-check{
  background-color: #d3d9da !important;
}
.qadpt-tabs-container button {
 text-transform: capitalize;
  padding: 10px 15px;

}
.qadpt-tabs-container .Mui-selected {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #000 !important;
}
.qadpt-tabs-container .MuiTabs-indicator {
  background-color: var(--primarycolor) !important;
}
.qadpt-webgird{
  height: calc(100vh - 220px);
}
.qadpt-webgird .MuiDataGrid-main {
  --DataGrid-topContainerHeight: 40px !important;
}
.qadpt-webgird .MuiDataGrid-row {
  background-color: var(--white-color);
  border-radius: var(--button-border-radius);
}
.qadpt-webgird .MuiDataGrid-root {
  border: none;
}
.qadpt-webgird .MuiDataGrid-columnHeaders {
  background: var(--grid-head-background);
  color: black;
  border-right: 1px solid #f6eeee;
  height: 40px !important;
}
.qadpt-webgird .MuiDataGrid-cell {
  border-bottom: none;
}
.qadpt-webgird .MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select {
  padding-right: 24px !important;
}
.MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select {
  padding-right: 24px !important;
}


.qadpt-webgird .MuiDataGrid-cell:nth-child(2),
.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(2) {
  width: 35% !important;
  max-width: 35% !important;
  min-width: 35%;
}

.qadpt-webgird .MuiDataGrid-cell:nth-child(3),
.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(3) {
  width: 30% !important;
  max-width: 30% !important;
  min-width: 30%;
}

.qadpt-webgird .MuiDataGrid-cell:nth-child(4),
.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(4) {
  width: 30% !important;
  max-width: 30% !important;
  min-width: 30%;
  /* text-align: right;
  right: 6px;
  position: absolute; */
}

.qadpt-webgird .MuiDataGrid-cell button{
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #F1F2F8;
  height: 30px;
  margin-right: 5px;
  }
  /* .qadpt-webgird .qadpt-grdcont{
    --DataGrid-topContainerHeight: 0px !important;
   } */

  .qadpt-webclonepopup .MuiPaper-root.MuiDialog-paper {
    border-radius: 4px;
		width: 400px;
 }
  .qadpt-webclonepopup .qadpt-title {
    font-size: 18px !important;
    font-weight: 600;
    padding: 15px !important;
    border-bottom: 1px solid var(--border-color);

 }
 .qadpt-webclonepopup input{
  padding: 5px !important;
  margin-top:10px !important;
 }
  .qadpt-webclonepopup .qadpt-close {
    position: absolute !important;
    top: 15px;
    right: 20px;
    color: #ccc;
 }
  .qadpt-webclonepopup .qadpt-close svg {
    font-size: 18px !important;
 }
  .qadpt-webclonepopup .qadpt-subtitle {
    margin-top: 20px;
    font-size: 14px;
 }
  .qadpt-webclonepopup .MuiDialogContent-root {
    padding: 15px !important;
    min-height: 80px;
    align-content: center;
 }
 .qadpt-webclonepopup .MuiDialogActions-root  {
  padding: 15px !important;
  border-top: 1px solid var(--border-color);
 }

  .qadpt-webclonepopup .MuiDialogActions-root .MuiButton-root {
    background-color: var(--primarycolor);
    border-radius: 4px;
    line-height: var(--button-lineheight);
    padding: var(--button-padding);
    text-transform: capitalize !important;
 }


 /*-------------------- AI UI changes-----------------------------*/

.fixed-chat-button {
  position: fixed;
  bottom: 20px;
  left: 220px;
  z-index: 9999;
}

.chat-btn {
  background-color: var(--primarycolor);
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 20px;
  cursor: pointer;
}

.chat-btn:hover {
  background-color: var(--primarycolor);
}


.chat-popup {
  position: fixed;
  bottom: 80px;
  left: 20px;
  background-color: white;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 15px;
  z-index: 9999;
  width: 250px;
  height: 110px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 20px;
}

.chat-textarea {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 10px;
  font-size: 14px;
  resize: none; /* Prevent resizing */
  outline: none;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.chat-textarea:focus {
  border-color: var(--primarycolor);
  outline: none;
}


.Mui-error {
  border-color: #e74c3c !important;
}

.chat-submit-btn {
  background-color: var(--primarycolor);
  color: white;
  border: none;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.chat-submit-btn:hover {
  background-color:var(--primarycolor);
}


.chat-close-icon {
  cursor: pointer;
  font-size: 18px;
  color: #888;
  align-self: flex-end;
  transition: color 0.3s;
}

.chat-close-icon:hover {
  color: #e74c3c;
}


.chat-close-icon {
  position: absolute;
  top: 0px;
  right: 6px;
  background-color: transparent;
  color: #ff4d4d;
  font-size: 24px;
  cursor: pointer;
}

.chat-close-icon:hover {
  color: #ff3333;
}

.chat-submit-btn {
  background-color: var(--primarycolor);
  color: white;
  border: none;
  padding: 4px 20px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  outline: none;
}

.chat-submit-btn:hover {
  background-color: var(--primarycolor);
  transform: scale(1.05);

.chat-submit-btn:active {
  background-color: var(--primarycolor);
  transform: scale(0.95);
}

.chat-submit-btn:disabled {
  background-color: #d6d6d6;
  cursor: not-allowed;
  opacity: 0.6;
}
}
/* @import "/src/assets/icon.css";

:root {
	--font-family: Poppins, Proxima Nova, arial, serif;
    --primarycolor: #5F9EA0;
    --ext-background: #F6EEEE;
    --border-color: #ccc;
    --white-color: #fff;
    --back-light-color:#EAE2E2;
    --button-border-radius: 12px !important
}
*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i) {
	font-family: var(--font-family) !important;
}
body {
	margin: 0;
	font-family: var(--font-family) !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

@font-face {
	font-family: "Gotham Pro";
	font-style: normal;
	src: local("Gotham Pro"), local("Gotham Pro"), url("../../../assets/fonts/GothamPro.woff2") format("woff2"),
		url("../../../assets/fonts/GothamPro.woff") format("woff");
}

@font-face {
	font-family: "Proxima Nova";
	font-style: normal;
	src: local("Proxima Nova"), local("ProximaNova-Regular"),
		url("../../../assets/fonts/ProximaNova-Regular.woff2") format("woff2"),
		url("../../../assets/fonts/ProximaNova-Regular.woff") format("woff");
}

@font-face {
	font-family: "qadapt-icons";
	src: url("../../../assets/fonts/qadapt-icons.eot?qmcsfb");
	src: url("../../../assets/fonts/qadapt-icons.eot?qmcsfb#iefix") format("embedded-opentype"),
		url("../../../assets/fonts/qadapt-icons.ttf?qmcsfb") format("truetype"),
		url("../../../assets/fonts/qadapt-icons.woff?qmcsfb") format("woff"),
		url("../../../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons") format("svg");
	font-weight: normal;
	font-style: normal;
	font-display: block;
} */
/* Side menu */
 .side-menu .menu-list {
	 list-style: none;
	 padding: 0;
	 margin: 0;
}
 .side-menu .menu-list .menu-item {
  padding: 6px 12px 12px 12px;
  margin: 10px 0;
  border-radius: 12px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border: 1px solid #e4e4e4;
  list-style-type: none !important;
}
.side-menu .menu-list .menu-item:hover {border: 1px solid var(--primarycolor) !important;}

 /* .side-menu .menu-list .menu-item .menu-content {
  display: flex;
  width: 100%;
  flex-direction: column;
} */
 .side-menu .menu-list .menu-item .menu-text {
	 /* display: flex;
	 flex-direction: column; */
   font-size: 12px !important;
   line-height: initial !important;
}
 .side-menu .menu-list .menu-item .menu-text .menu-title {
	 font-size: 16px !important;
	 font-weight: 600;
	 margin-bottom: 4px;
	 text-align: left;
   color: #000 !important;
}
 .side-menu .menu-list .menu-item .menu-text .menu-description {
	 font-size: 12px;
	 color: #9c9c9c;
	 text-align: left;
   line-height: initial !important;
}
 .side-menu .menu-list .menu-item .icons {
	 display: flex;
	 align-items: center;
   margin-right: 10px;
}

  .menu-item.disabled {
    opacity: 0.5;
    pointer-events: none;
}
.menu-item.active {
 background: rgba(95, 158, 160, 0.1);
  border: 1px solid var(--primarycolor) !important;
}

.qadpt-gud-menupopup {
  margin-left: 300px;
  height: calc(100vh + 13px);
  border-radius: 30px;
  z-index: 99999 !important;
}
.qadpt-gud-menupopup .MuiPaper-root{
  border-radius: 25px;
}
.qadpt-gud-menupopup-content {
  background: var(--ext-background);
  padding-bottom: 0 !important;
  z-index: 9999;
}
.qadpt-head {
  margin: 0 -20px;
  padding: 12px 20px 5px 20px;
}
.qadpt-titsection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
 /* Ensure titsection takes up the full width */
}
.qadpt-extsearch {
  background: var(--white-color);
  border-radius: 10px;
  width: 70%;
}
.qadpt-extsearch .MuiOutlinedInput-root {
  height: 40px;
  border-radius: 10px;
  border: 1px solid #a8a8a8;
}
.qadpt-extsearch .MuiOutlinedInput-input {
  /* height: inherit !important; */
  border: 0 !important;
  border-radius: inherit !important;
  /* line-height: inherit !important; */
  font-size: initial !important;
}
.qadpt-extsearch .MuiOutlinedInput-notchedOutline {
  border-radius: 10px !important;
  border : none !important;
}

.qadpt-right-part {
  display: flex;
  /* justify-content: flex-end;

  width: 25%; */
}
.qadpt-subhead{
display: flex;
align-items: center;
}
.qadpt-subhead .title{
font-size: 18px !important;
    font-weight: 600 !important;
    width: 100%;}
.qadpt-memberButton {
  background-color: var(--primarycolor) !important;
  border: none !important;
  border-radius: var(--button-border-radius) !important;
  color: var(--white-color) !important;
  cursor: pointer !important;
  font-size: 14px !important;
  padding: 10px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center;
}
.qadpt-memberButton i {
  margin-right: 10px;
}
.qadpt-memberButton span {
  font-size: 14px;
}

.qadpt-tabs-container {
  border-bottom: 1px solid #ccc;
  margin-bottom: 10px;


}
.qadpt-memberButton.qadpt-check{
  background-color: #d3d9da !important;
}
.qadpt-tabs-container button {
 text-transform: capitalize;
  padding: 10px 15px;

}
.qadpt-tabs-container .Mui-selected {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #000 !important;
}
.qadpt-tabs-container .MuiTabs-indicator {
  background-color: var(--primarycolor) !important;
}
.qadpt-webgird{
  height: calc(100vh - 220px);
}
.qadpt-webgird .MuiDataGrid-main {
  --DataGrid-topContainerHeight: 40px !important;
}
.qadpt-webgird .MuiDataGrid-row {
  background-color: var(--white-color);
  border-radius: var(--button-border-radius);
}
.qadpt-webgird .MuiDataGrid-root {
  border: none;
}
.qadpt-webgird .MuiDataGrid-columnHeaders {
  background: var(--grid-head-background);
  color: black;
  border-right: 1px solid #f6eeee;
  height: 40px !important;
}
.qadpt-webgird .MuiDataGrid-cell {
  border-bottom: none;
}
.qadpt-webgird .MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select {
  padding-right: 24px !important;
}
.MuiTablePagination-root .MuiSelect-select.MuiTablePagination-select {
  padding-right: 24px !important;
}


.qadpt-webgird .MuiDataGrid-cell:nth-child(2),
.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(2) {
  width: 35% !important;
  max-width: 35% !important;
  min-width: 35%;
}

.qadpt-webgird .MuiDataGrid-cell:nth-child(3),
.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(3) {
  width: 30% !important;
  max-width: 30% !important;
  min-width: 30%;
}

.qadpt-webgird .MuiDataGrid-cell:nth-child(4),
.qadpt-webgird .MuiDataGrid-columnHeader:nth-child(4) {
  width: 30% !important;
  max-width: 30% !important;
  min-width: 30%;
  /* text-align: right;
  right: 6px;
  position: absolute; */
}

.qadpt-webgird .MuiDataGrid-cell button{
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #F1F2F8;
  height: 30px;
  margin-right: 5px;
  }
  /* .qadpt-webgird .qadpt-grdcont{
    --DataGrid-topContainerHeight: 0px !important;
   } */

  .qadpt-webclonepopup .MuiPaper-root.MuiDialog-paper {
    border-radius: 4px;
		width: 400px;
 }
  .qadpt-webclonepopup .qadpt-title {
    font-size: 18px !important;
    font-weight: 600;
    padding: 15px !important;
    border-bottom: 1px solid var(--border-color);

 }
 .qadpt-webclonepopup input{
  padding: 5px !important;
  margin-top:10px !important;
 }
  .qadpt-webclonepopup .qadpt-close {
    position: absolute !important;
    top: 15px;
    right: 20px;
    color: #ccc;
 }
  .qadpt-webclonepopup .qadpt-close svg {
    font-size: 18px !important;
 }
  .qadpt-webclonepopup .qadpt-subtitle {
    margin-top: 20px;
    font-size: 14px;
 }
  .qadpt-webclonepopup .MuiDialogContent-root {
    padding: 15px !important;
    min-height: 80px;
    align-content: center;
 }
 .qadpt-webclonepopup .MuiDialogActions-root  {
  padding: 15px !important;
  border-top: 1px solid var(--border-color);
 }

  .qadpt-webclonepopup .MuiDialogActions-root .MuiButton-root {
    background-color: var(--primarycolor);
    border-radius: 4px;
    line-height: var(--button-lineheight);
    padding: var(--button-padding);
    text-transform: capitalize !important;
 }


 /*-------------------- AI UI changes-----------------------------*/

.fixed-chat-button {
  position: fixed;
  bottom: 20px;
  left: 220px;
  z-index: 9999;
}

.chat-btn {
  background-color: var(--primarycolor);
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 20px;
  cursor: pointer;
}

.chat-btn:hover {
  background-color: var(--primarycolor);
}


.chat-popup {
  position: fixed;
  bottom: 80px;
  left: 20px;
  background-color: rgb(241, 194, 194);
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 15px;
  z-index: 9999;
  width: 250px;
  height: 110px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 20px;
}

.chat-textarea {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 10px;
  font-size: 14px;
  resize: none; /* Prevent resizing */
  outline: none;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.chat-textarea:focus {
  border-color: var(--primarycolor);
  outline: none;
}


.Mui-error {
  border-color: #e74c3c !important;
}

.chat-submit-btn {
  background-color: var(--primarycolor);
  color: white;
  border: none;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.chat-submit-btn:hover {
  background-color:var(--primarycolor);
}


.chat-close-icon {
  cursor: pointer;
  font-size: 18px;
  color: #888;
  align-self: flex-end;
  transition: color 0.3s;
}

.chat-close-icon:hover {
  color: #e74c3c;
}


.chat-close-icon {
  position: absolute;
  top: 0px;
  right: 6px;
  background-color: transparent;
  color: #ff4d4d;
  font-size: 24px;
  cursor: pointer;
}

.chat-close-icon:hover {
  color: #ff3333;
}

.chat-submit-btn {
  background-color: var(--primarycolor);
  color: white;
  border: none;
  padding: 4px 20px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  outline: none;
}

.chat-submit-btn:hover {
  background-color: var(--primarycolor);
  transform: scale(1.05);

.chat-submit-btn:active {
  background-color: var(--primarycolor);
  transform: scale(0.95);
}

.chat-submit-btn:disabled {
  background-color: #d6d6d6;
  cursor: not-allowed;
  opacity: 0.6;
}
}

/* Scraping overlay styles */
.scraping-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.scraping-message {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 300px;
  width: 100%;
}

.scraping-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primarycolor);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stop-scraping-button {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: bold;
  margin-top: 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.stop-scraping-button:hover {
  background-color: #c0392b;
}