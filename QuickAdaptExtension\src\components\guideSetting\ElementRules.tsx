import React, { useState } from "react";
import { Box, Button, Typography, IconButton } from "@mui/material";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";

// import Draggable from "react-draggable";

const ElementRules = () => {
	const [isOpen, setIsOpen] = useState(true);
	const handleClose = () => {
		setIsOpen(false); // Close the popup when close button is clicked
	};

	if (!isOpen) return null;
	return (
		// <Draggable>
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				{/* Header */}
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
						{/* Header */}
					</IconButton>
					<div className="qadpt-title">Element Rules</div>
					{/* Close Button */}
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>

				{/* Choose Element Button */}
				<Box
					sx={{
						display: "flex",
						justifyContent: "center",
						alignItems: "center",
						backgroundColor: "#f9f7f7",
						borderRadius: "10px",
						padding: "12px",
						marginBottom: "12px",
					}}
				>
					<Button
						variant="outlined"
						sx={{
							textTransform: "none",
							borderRadius: "8px",
							borderColor: "#d1bcbc",
							color: "#4c9999",
							width: "100%",
						}}
					>
						Choose Element
					</Button>
				</Box>

				{/* Present & Absent Buttons */}
				<Box
					sx={{
						display: "flex",
						justifyContent: "space-between",
						gap: "12px",
						marginBottom: "12px",
					}}
				>
					<Button
						variant="outlined"
						sx={{
							textTransform: "none",
							borderRadius: "8px",
							borderColor: "#d1bcbc",
							color: "#4c9999",
							flex: 1,
						}}
					>
						Present
					</Button>
					<Button
						variant="outlined"
						sx={{
							textTransform: "none",
							borderRadius: "8px",
							borderColor: "#d1bcbc",
							color: "#4c9999",
							flex: 1,
						}}
					>
						Absent
					</Button>
				</Box>

				{/* Add Element Button */}
				<Box>
					<Button
						variant="outlined"
						sx={{
							textTransform: "none",
							borderRadius: "8px",
							borderColor: "#d1bcbc",
							color: "#4c9999",
							width: "100%",
							padding: "12px",
						}}
					>
						Add Element
					</Button>
				</Box>
			</div>
		</div>
		//</Draggable>
	);
};

export default ElementRules;
