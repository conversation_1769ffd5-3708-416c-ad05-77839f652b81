import React, { useEffect, useState } from "react";
import {
	<PERSON>,
	Typo<PERSON>,
	Popover,
	IconButton,
	TextField,
	MenuItem,
	Button,
	Tooltip,
	Snackbar,
	Alert
} from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";

import { FileUpload } from "../../../models/FileUpload";

import {
	uploadfile,
	hyperlink,
	files,
	uploadicon,
	replaceimageicon,
	copyicon,
	deleteicon,
	sectionheight,
	Settings,
	CrossIcon,
} from "../../../assets/icons/icons";
import useDrawerStore, {
	IMG_CONTAINER_DEFAULT_HEIGHT,
	IMG_CONTAINER_MAX_HEIGHT,
	IMG_CONTAINER_MIN_HEIGHT,
	IMG_OBJECT_FIT,
	IMG_STEP_VALUE,
} from "../../../store/drawerStore";
import { Chrome<PERSON>icker, ColorResult } from "react-color";
import "./PopupSections.css";
import SelectImageFromApplication from "../../common/SelectImageFromApplication";

const ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {
	const {
		uploadImage,
		imagesContainer,
		imageAnchorEl,
		setImageAnchorEl,
		replaceImage,
		cloneImageContainer,
		deleteImageContainer,
		updateImageContainer,
		toggleFit,
		setImageSrc: storeImageSrc,
	} = useDrawerStore((state) => state);
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState('');
	const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');

	const [snackbarKey, setSnackbarKey] = useState<number>(0); 

	const openSnackbar = () => {
		setSnackbarKey(prev => prev + 1);
		setSnackbarOpen(true);
	};
	const closeSnackbar = () => {
		setSnackbarOpen(false);
	};
	const [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({
		currentContainerId: "",
		isOpen: false,
	});
	const [imageLink, setImageLink] = useState<string>("");
	const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);
	const [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{
		currentContainerId: string;
		isImage: boolean;
		height: number;
	}>({ currentContainerId: "", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });

	const [selectedAction, setSelectedAction] = useState("none");
	const [isModelOpen, setModelOpen] = useState(false);
	const [formOfUpload, setFormOfUpload] = useState<String>("");
	const [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);
	const [selectedColor, setSelectedColor] = useState<string>("#313030");
	const [isReplaceImage, setReplaceImage] = useState(false);


	const openSettingsPopover = Boolean(settingsAnchorEl);

	const handleActionChange = (event: any) => {
		setSelectedAction(event.target.value);
	};

	const handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {
		setSettingsAnchorEl(event.currentTarget);
	};

	const handleCloseSettingsPopover = () => {
		setSettingsAnchorEl(null);
	};
	const imageContainerStyle: React.CSSProperties = {
		width: "100%",
		height: "100%",
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		padding: 0,
		margin: 0,
		overflow: "hidden",
	};

	const imageStyle: React.CSSProperties = {
		width: "100%",
		height: "100%",
		margin: 0,
		padding: 0,
		borderRadius: "0",
	};

	const iconRowStyle: React.CSSProperties = {
		display: "flex",
		justifyContent: "center",
		gap: "16px",
		marginTop: "10px",
	};

	const iconTextStyle: React.CSSProperties = {
		display: "flex",
		flexDirection: "column",
		alignItems: "center",
		justifyContent: "center",
		width: "100%",
	};

	const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const parts = file.name.split('.');
   			const extension = parts.pop();

   		 // Check for double extensions (e.g. file.html.png) or missing/invalid extension
   			 if (parts.length > 1 || !extension ) {
			  setSnackbarMessage("Uploaded file name should not contain any special character");
       		 setSnackbarSeverity("error");
			 setSnackbarOpen(true);
			 event.target.value = '';
      		 return;
			 
   			 }
			 if(file.name.length > 128){
				setSnackbarMessage("File name should not exceed 128 characters");
       			setSnackbarSeverity("error");
			 	setSnackbarOpen(true);
			 	event.target.value = '';
      		 	return;
			 }
			setImageName(event.target.files?.[0].name);

			const reader = new FileReader();
			reader.onloadend = () => {
				const base64Image = reader.result as string;
				storeImageSrc(base64Image);
				setImageSrc(base64Image);
				uploadImage(imageAnchorEl.containerId, {
					altText: file.name,
					id: crypto.randomUUID(),
					url: base64Image,
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
			};
			reader.readAsDataURL(file);
		}
		setModelOpen(false);
	};

	const handleImageUploadFormApp = (file: FileUpload) => {
		if (file) {
			storeImageSrc(file.Url);
			setImageSrc(file.Url);
			if (isReplaceImage) {
				replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {
					altText: file.FileName,
					id: imageAnchorEl.buttonId,
					url: file.Url,
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
				setReplaceImage(false);
			} else {
				uploadImage(imageAnchorEl.containerId, {
					altText: file.FileName,
					id: crypto.randomUUID(), // Use existing ID
					url: file.Url, // Directly use the URL
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
			}
		}
		setModelOpen(false);
	};
	const handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onloadend = () => {
				replaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {
					altText: file.name,
					id: imageAnchorEl.buttonId,
					url: reader.result,
					backgroundColor: "#ffffff",
					objectFit: IMG_OBJECT_FIT,
				});
			};
			reader.readAsDataURL(file);
		}
	};

	const handleClick = (
		event: React.MouseEvent<HTMLElement>,
		containerId: string,
		imageId: string,
		isImage: boolean,
		currentHeight: number
	) => {
		// @ts-ignore
		if (["file-upload", "hyperlink"].includes(event.target.id)) return;
		setImageAnchorEl({
			buttonId: imageId,
			containerId: containerId,
			// @ts-ignore
			value: event.currentTarget,
		});
		setSettingsAnchorEl(null);
		setCurrentImageSectionInfo({
			currentContainerId: containerId,
			isImage,
			height: currentHeight,
		});
		setShowHyperlinkInput({
			currentContainerId: "",
			isOpen: false,
		});
	};

	const handleClose = () => {
		setImageAnchorEl({
			buttonId: "",
			containerId: "",
			// @ts-ignore
			value: null,
		});
	};

	const open = Boolean(imageAnchorEl.value);
	const colorPickerOpen = Boolean(colorPickerAnchorEl);

	const id = open ? "image-popover" : undefined;

	const handleIncreaseHeight = (prevHeight: number) => {
		if (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;
		const newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);
		updateImageContainer(imageAnchorEl.containerId, "style", {
			height: newHeight,
		});
		setCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));
	};

	const handleDecreaseHeight = (prevHeight: number) => {
		if (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;
		const newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);
		updateImageContainer(imageAnchorEl.containerId, "style", {
			height: newHeight,
		});
		setCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));
	};

	const triggerImageUpload = () => {
		document.getElementById("replace-upload")?.click();
		// setModelOpen(true);
		// setReplaceImage(true);
	};

	const currentContainerColor =
		imagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || "transparent";
	// Function to delete the section
	const handleDeleteSection = () => {
		setImageAnchorEl({
			buttonId: "",
			containerId: "",
			// @ts-ignore
			value: null,
		});

		// Call the delete function from the store
		deleteImageContainer(imageAnchorEl.containerId);

		// Call the onDelete callback if provided
		if (onDelete) {
			onDelete();
		}
	};


	const handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {
		if (event.key === "Enter" && imageLink) {
			uploadImage(imageAnchorEl.containerId, {
				altText: "New Image",
				id: crypto.randomUUID(),
				url: imageLink,
				backgroundColor: "transparent",
				objectFit: IMG_OBJECT_FIT,
			});
			setShowHyperlinkInput({
				currentContainerId: "",
				isOpen: false,
			});
		}
	};

	const handleCloneImgContainer = () => {
		// Check if cloning is disabled due to section limits
		if (isCloneDisabled) {
			return; // Don't clone if limit is reached
		}

		// Call the clone function from the store
		cloneImageContainer(imageAnchorEl.containerId);

		// Call the onClone callback if provided
		if (onClone) {
			onClone();
		}
	};

	const handleCloseColorPicker = () => {
		setColorPickerAnchorEl(null);
	};

	const handleColorChange = (color: ColorResult) => {
		setSelectedColor(color.hex);
		updateImageContainer(imageAnchorEl.containerId, "style", {
			backgroundColor: color.hex,
		});
	};

	const handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {
		setColorPickerAnchorEl(event.currentTarget);
	};

	return (
		<>
			{imagesContainer.map((item) => {
				const imageSrc = item.images[0]?.url;
				const imageId = item.images[0]?.id;
				const objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;
				const currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;
				const id = item.id;
				return (
					<Box
						key={id}
						sx={{
							width: "100%",
							height: "100%",
							display: "flex",
							flexDirection: "column",
							justifyContent: "flex-start",
							alignItems: "center",
							// padding: "5px",
							margin: "0px",
							overflow: "auto",
						}}
					>
						<Box
							sx={{
								...imageContainerStyle,
								backgroundColor: item.style.backgroundColor,
								height: `${item.style.height}px`,
							}}
							onClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}
							component={"div"}
							id={id}
							onMouseOver={() => {
								setImageAnchorEl({
									buttonId: imageId,
									containerId: id,
									value: null,
								});
							}}
						>
							{imageSrc ? (
								<img
									src={imageSrc}
									alt="Uploaded"
									style={{ ...imageStyle, objectFit }}
								/>
							) : (
								<Box
									sx={{
										textAlign: "center",
										width: "100%",
										height: "100%",
										display: "flex",
										flexDirection: "column",
										justifyContent: "center",
									}}
								>
									<Box
										sx={iconTextStyle}
										component={"div"}
									>
										<span
											dangerouslySetInnerHTML={{ __html: uploadfile }}
											style={{ display: "inline-block" }}
										/>
										<Typography
											variant="h6"
											align="center"
											sx={{ fontSize: "14px", fontWeight: "600" }}
										>
											Upload file
										</Typography>
									</Box>

									<Typography
										variant="body2"
										align="center"
										color="textSecondary"
										sx={{ fontSize: "14px" }}
									>
										Drag & Drop to upload file
									</Typography>
									<Typography
										variant="body2"
										align="center"
										color="textSecondary"
										sx={{ marginTop: "8px", fontSize: "14px" }}
									>
										Or
									</Typography>
									{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (
										<TextField
											value={imageLink}
											onChange={(e) => setImageLink(e.target.value)}
											onKeyDown={handleLinkSubmit}
											autoFocus
										/>
									) : (
										<Box sx={iconRowStyle}>
			<Tooltip title="Coming soon">
  <div style={{ pointerEvents: "auto", cursor:"pointer"}}>
    <span
      dangerouslySetInnerHTML={{ __html: hyperlink }}
      style={{
        color: "black",
        cursor: "pointer",
        fontSize: "32px",
        opacity: "0.5",
        pointerEvents: "none",
      }}
      id="hyperlink"
													className="qadpt-image-upload"
    />
  </div>
</Tooltip>


													<Tooltip title="Coming soon">
														<span
															onClick={(event) => {
																//setModelOpen(true);
															}}
												dangerouslySetInnerHTML={{ __html: files }}
												style={{ color: "black", cursor: "pointer", fontSize: "32px", opacity: "0.5" }}
												id="folder"
												className="qadpt-image-upload"
												//title="Coming Soon"
														/>
													</Tooltip>
													<Tooltip title="Upload File">
											<span
															onClick={(event) => {

													event?.stopPropagation();
													document.getElementById("file-upload")?.click();
												}}
												id="file-upload1"
												className="qadpt-image-upload"
												dangerouslySetInnerHTML={{ __html: uploadicon }}
												style={{ color: "black", cursor: "pointer", fontSize: "32px" }}
														/>
														</Tooltip>
											<input
												type="file"
												id="file-upload"
												style={{ display: "none" }}
												accept="image/*"
												onChange={handleImageUpload}
											/>
											<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>
												<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
													{snackbarMessage}
												</Alert>
											</Snackbar>
										</Box>
									)}
								</Box>
							)}
						</Box>
					</Box>
				);
			})}
			{Boolean(imageAnchorEl.value) ? (
				<Popover
					className="qadpt-imgsec-popover"
					id={id}
					open={open}
					anchorEl={imageAnchorEl.value}
					onClose={handleClose}
					anchorOrigin={{
						vertical: "top",
						horizontal: "center",
					}}
					transformOrigin={{
						vertical: "bottom",
						horizontal: "center",
					}}
				>
					<Box
						sx={{
							display: "flex",
							alignItems: "center",
							gap: "20px",
							height: "100%",
							padding: "0 10px",
							fontSize: "12px",
						}}
					>
						<Box sx={{ display: "flex" }}>
							{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&
							currentImageSectionInfo.isImage ? (
								<>
									<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />
									<Typography
										fontSize="12px"
										marginLeft={"5px"}
										onClick={triggerImageUpload}
									>
										Replace Image
									</Typography>
									<input
										type="file"
										id="replace-upload"
										style={{ display: "none" }}
										accept="image/*"
										onChange={handleReplaceImage}
									/>
								</>
							) : null}
						</Box>

<Box
							className="qadpt-tool-items"
							sx={{ display: "flex", alignItems: "center" }}
						>
							<span dangerouslySetInnerHTML={{ __html: sectionheight }} />
							<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? `Minimum height reached` : "Decrease height"}>
								<span>
									<IconButton
										onClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}
										size="small"
										disabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}
										sx={{
											opacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,
											cursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'
										}}
									>
										<RemoveIcon fontSize="small" />
									</IconButton>
								</span>
							</Tooltip>
							<Typography fontSize="12px">{currentImageSectionInfo.height}</Typography>
							<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? `Maximum height reached` : "Increase height"}>
								<span>
									<IconButton
										onClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}
										size="small"
										disabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}
										sx={{
											opacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,
											cursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'
										}}
									>
										<AddIcon fontSize="small" />
									</IconButton>
								</span>
							</Tooltip>
						</Box>
						<Tooltip title="Settings">
						<Box className="qadpt-tool-items">
							<Box className="qadpt-tool-items">
								<IconButton
									size="small"
									onClick={handleSettingsClick}
								>
									<span
										dangerouslySetInnerHTML={{ __html: Settings }}
										style={{ color: "black" }}
									/>
								</IconButton>
							</Box>

								<Popover
																		className="qadpt-imgset"
								open={openSettingsPopover}
								anchorEl={settingsAnchorEl}
								onClose={handleCloseSettingsPopover}
								anchorOrigin={{
									vertical: "center",
									horizontal: "right",
								}}
								transformOrigin={{
									vertical: "center",
									horizontal: "left",
								}}
								slotProps={{
									paper: {
									sx: {
										mt: 12,
										ml: 20,
										width: "205px",
									},
									},
								}}
							>
								<Box p={2}>
									<Box
										display="flex"
										justifyContent="space-between"
										alignItems="center"
									>
										<Typography
											variant="subtitle1"
											sx={{ color: "rgba(95, 158, 160, 1)" }}
										>
											Image Properties
										</Typography>
										<IconButton
											size="small"
											onClick={handleCloseSettingsPopover}
										>
											<span
												dangerouslySetInnerHTML={{ __html: CrossIcon }}
												style={{ color: "black" }}
											/>
										</IconButton>
										</Box>
										<Tooltip title="Coming soon">
									<Box mt={2}>
										<Typography
											variant="body2"
													color="textSecondary"
													sx={{ marginBottom: "10px" }}
										>
											Image Actions
										</Typography>
										<TextField
											select
											fullWidth
											variant="outlined"
											size="small"
											value={selectedAction}
											onChange={handleActionChange}
											sx={{
												"& .MuiOutlinedInput-root": {
													borderColor: "rgba(246, 238, 238, 1)",
												},
											}}
											disabled
										>
											<MenuItem value="none">None</MenuItem>
											<MenuItem value="specificStep">Specific Step</MenuItem>
											<MenuItem value="openUrl">Open URL</MenuItem>
											<MenuItem value="clickElement">Click Element</MenuItem>
											<MenuItem value="startTour">Start Tour</MenuItem>
											<MenuItem value="startMicroSurvey">Start Micro Survey</MenuItem>
										</TextField>
									</Box>
									</Tooltip>
									<Box mt={2}>
										<Typography
											variant="body2"
											color="textSecondary"
										>
											Image Formatting
										</Typography>
										<Box
											display="flex"
											gap={1}
											mt={1}
										>
											{["Fill", "Fit"].map((item) => {
												// Get current image's objectFit to determine selected state
												const currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);
												const currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);
												const currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;

												// Determine if this button should be selected
												const isSelected = (item === "Fill" && currentObjectFit === "cover") ||
																  (item === "Fit" && currentObjectFit === "contain");

												return (
													<Button
														key={item}
														onClick={() =>
															toggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as "Fit" | "Fill")
														}
														variant="outlined"
														size="small"
														sx={{
															width: "88.5px",
															height: "41px",
															padding: "10px 12px",
															gap: "12px",
															borderRadius: "6px 6px 6px 6px",
															border:
																isSelected
																	? "1px solid rgba(95, 158, 160, 1)"
																	: "1px solid rgba(246, 238, 238, 1)",
															backgroundColor:
																isSelected ? "rgba(95, 158, 160, 0.2)" : "rgba(246, 238, 238, 0.5)",
															backgroundBlendMode: "multiply",
															color: "black",
															"&:hover": {
																backgroundColor:
																	isSelected ? "rgba(95, 158, 160, 0.3)" : "rgba(246, 238, 238, 0.6)",
															},
														}}
													>
														{item}
													</Button>
												);
											})}
										</Box>
									</Box>
								</Box>
							</Popover>
							</Box>
						</Tooltip>
						<Tooltip title="Background Color">
						<Box className="qadpt-tool-items">
							<IconButton
								onClick={handleBackgroundColorClick}
								size="small"
							>
								<span
								style={{
									backgroundColor: selectedColor,
									borderRadius: "100%",
									width: "20px",
									height: "20px",
								    display: "inline-block",
									marginTop:"-3px"
								}} />
							</IconButton>
						</Box>
						</Tooltip>
						<Tooltip title={isCloneDisabled ? "Maximum limit of 3 Image sections reached" : "Clone Section"}>
						<Box className="qadpt-tool-items">
							<IconButton
								onClick={handleCloneImgContainer}
								size="small"
								disabled={isCloneDisabled}
							>
								<span
									dangerouslySetInnerHTML={{ __html: copyicon }}
									style={{ opacity: isCloneDisabled ? 0.5 : 1 }}
								/>
							</IconButton>

							</Box>
						</Tooltip>
						<Tooltip title="Delete Section">

						<Box className="qadpt-tool-items">
							<IconButton
								onClick={handleDeleteSection}
								size="small"
							>
								<span dangerouslySetInnerHTML={{ __html: deleteicon }} style={{ marginTop: "-3px" }}/>
							</IconButton>
							</Box>
							</Tooltip>
					</Box>
				</Popover>
			) : null}
			{
				isModelOpen && (
					<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>
				)
			}
			<Popover
				open={colorPickerOpen}
				anchorEl={colorPickerAnchorEl}
				onClose={handleCloseColorPicker}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "center",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "center",
				}}
			>
				<Box>
					<ChromePicker
						color={currentContainerColor}
						onChange={handleColorChange}
					/>
							<style>
    {`
      .chrome-picker input {
        padding: 0 !important;
      }
    `}
  </style>
				</Box>
			</Popover>
		</>
	);
};

export default ImageSection;
