import React, { useEffect, useState } from "react";
import { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import RadioButtonCheckedIcon from "@mui/icons-material/RadioButtonChecked";
// import Draggable from "react-draggable";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import useDrawerStore from "../../store/drawerStore";
import { defaultDots, topLeft, topCenter, topRight, middleLeft, middleCenter, middleRight, bottomLeft, bottomMiddle, bottomRight, topcenter, warning } from "../../assets/icons/icons";

const ChecklistCanvasSettings = ({ zindeex, setZindeex, setShowChecklistCanvasSettings, selectedTemplate }: any) => {
	const {
		setCanvasSetting,
		borderColor,
		announcementJson,
		width,
		setWidth,
		backgroundColor,
		setBorderColor,
		setBackgroundColor,
		borderRadius,
		setBorderRadius,
		Annpadding,
		setAnnPadding,
		AnnborderSize,
		setAnnBorderSize,
		Bposition,
		setBposition,
		checklistGuideMetaData,
		updateChecklistCanvas,
		setIsUnSavedChanges,
		isUnSavedChanges,
	} = useDrawerStore((state: any) => state);

	const [isOpen, setIsOpen] = useState(true);

	// Error states for validation
	const [heightError, setHeightError] = useState(false);
	const [widthError, setWidthError] = useState(false);
	const [cornerRadiusError, setCornerRadiusError] = useState(false);
	const [borderWidthError, setBorderWidthError] = useState(false);


	const [checklistCanvasProperties, setChecklistCanvasProperties] = useState<any>(() => {
		const initialchecklistCanvasProperties = checklistGuideMetaData[0]?.canvas || {
	width: "930",
	height: "450",
	cornerRadius: "12",
	primaryColor: "#5F9EA0",
			borderColor: "",
	backgroundColor: "#ffffff",
			openByDefault: false,
			hideAfterCompletion: true,
	borderWidth:"0"
		};
		return initialchecklistCanvasProperties;
	});
	// State for tracking changes and apply button
	const [isDisabled, setIsDisabled] = useState(true);
	const [hasChanges, setHasChanges] = useState(false);
	const [initialState, setInitialState] = useState(checklistCanvasProperties);

	// Function to check if the Apply button should be enabled
	const updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {
		setIsDisabled(!changed || hasErrors);
	};

	// Effect to check for any changes compared to initial state
	useEffect(() => {
		// Compare current properties with initial state
		const hasAnyChanges = JSON.stringify(checklistCanvasProperties) !== JSON.stringify(initialState);
		setHasChanges(hasAnyChanges);

		// Check for validation errors
		const hasValidationErrors = heightError || widthError || cornerRadiusError || borderWidthError;

		updateApplyButtonState(hasAnyChanges, hasValidationErrors);
	}, [checklistCanvasProperties, initialState, heightError, widthError, cornerRadiusError, borderWidthError]);

	const handleBorderColorChange = (e: any) => setBorderColor(e.target.value);
	const handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);

	const handleClose = () => {
		setIsOpen(false);
		setShowChecklistCanvasSettings(false);

	};
	const onPropertyChange = (key: any, value: any) => {
		setChecklistCanvasProperties((prevState: any) => {
			const newState = {
				...prevState,
				[key]: value,
			};
			// Mark that changes have been made
			setHasChanges(true);
			return newState;
		});
	};

	const handleApplyChanges = () => {
		// Apply the changes - updateChecklistCanvas will handle recording the change for undo/redo
		updateChecklistCanvas(checklistCanvasProperties);
		// Update the initial state to the current state after applying changes
		setInitialState({ ...checklistCanvasProperties });
		// Reset the changes flag
		setHasChanges(false);
		// Disable the Apply button
		setIsDisabled(true);
		handleClose();
		setIsUnSavedChanges(true);
	};

	if (!isOpen) return null;

	return (
		//<Draggable>
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
						{/* Header */}
					</IconButton>
					<div className="qadpt-title">Canvas</div>
					{/* Close Button */}
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>

				{/* Position Grid */}
				<div className="qadpt-canblock">
				<div className="qadpt-controls">


					{/* Height Control */}
					<Box className="qadpt-control-box">
							<div className="qadpt-control-label">Height</div>
							<div>
						<TextField
							variant="outlined"
							value={checklistCanvasProperties.height}
							size="small"
							autoFocus
							className="qadpt-control-input"
							onChange={(e) => {
								// Only allow numeric input
								const value = e.target.value;
								if (value === '') {
									onPropertyChange("height", '0');
									setHeightError(false);
									return;
								}

								if (!/^-?\d*$/.test(value)) {
									return;
								}

								const inputValue = parseInt(value) || 0;

								// Validate height between 100px and 1000px
								if (inputValue < 400 || inputValue > 600) {
									setHeightError(true);
								} else {
									setHeightError(false);
								}

								onPropertyChange("height", value);
							}}

							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
							}}
							error={heightError}
								/>
								</div>
                        </Box>
					{heightError && (
						<Typography
							style={{
								fontSize: "12px",
								color: "#e9a971",
								textAlign: "left",
								top: "100%",
								left: 0,
								marginBottom: "5px",
								display: "flex",
							}}
						>
							<span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}
								dangerouslySetInnerHTML={{ __html: warning }}
							/>
							Value must be between 400px and 600px.
						</Typography>
					)}
                        <Box className="qadpt-control-box">
							<div className="qadpt-control-label">Width</div>
							<div>
						<TextField
							variant="outlined"
							value={checklistCanvasProperties.width}
							size="small"
							autoFocus
							className="qadpt-control-input"
							onChange={(e) => {
								// Only allow numeric input
								const value = e.target.value;
								if (value === '') {
									onPropertyChange("width", '0');
									setWidthError(false);
									return;
								}

								if (!/^-?\d*$/.test(value)) {
									return;
								}

								const inputValue = parseInt(value) || 0;

								// Validate width between 300px and 1200px
								if (inputValue < 300 || inputValue > 1200) {
									setWidthError(true);
								} else {
									setWidthError(false);
								}

								onPropertyChange("width", value);
							}}

							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
							}}
							error={widthError}
								/>
								</div>
					</Box>
					{widthError && (
						<Typography
							style={{
								fontSize: "12px",
								color: "#e9a971",
								textAlign: "left",
								top: "100%",
								left: 0,
								marginBottom: "5px",
								display: "flex",
							}}
						>
							<span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}
								dangerouslySetInnerHTML={{ __html: warning }}
							/>
							Value must be between 300px and 1200px.
						</Typography>
					)}
					{/* Corner Radius Control */}
					<Box className="qadpt-control-box">
							<div className="qadpt-control-label">Corner Radius</div>
							<div>
						<TextField
							variant="outlined"
							value={checklistCanvasProperties.cornerRadius}
							fullWidth
							size="small"
							className="qadpt-control-input"
							onChange={(e) => {
								// Only allow numeric input
								const value = e.target.value;
								if (value === '') {
									onPropertyChange("cornerRadius", '0');
									setCornerRadiusError(false);
									return;
								}

								if (!/^-?\d*$/.test(value)) {
									return;
								}

								const inputValue = parseInt(value) || 0;

								// Validate corner radius between 0px and 50px
								if (inputValue < 0 || inputValue > 50) {
									setCornerRadiusError(true);
								} else {
									setCornerRadiusError(false);
								}

								onPropertyChange("cornerRadius", value);
							}}

							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
							}}
							error={cornerRadiusError}
								/>
								</div>
						</Box>
					{cornerRadiusError && (
						<Typography
							style={{
								fontSize: "12px",
								color: "#e9a971",
								textAlign: "left",
								top: "100%",
								left: 0,
								marginBottom: "5px",
								display: "flex",
							}}
						>
							<span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}
								dangerouslySetInnerHTML={{ __html: warning }}
							/>
							Value must be between 0px and 50px.
						</Typography>
					)}

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">Border Width</div>
							<div>
						<TextField
							variant="outlined"
							value={checklistCanvasProperties.borderWidth}
							fullWidth
							size="small"
							className="qadpt-control-input"
							onChange={(e) => {
								// Only allow numeric input
								const value = e.target.value;
								if (value === '') {
									onPropertyChange("borderWidth", '0');
									setBorderWidthError(false);
									return;
								}

								if (!/^-?\d*$/.test(value)) {
									return;
								}

								const inputValue = parseInt(value) || 0;

								// Validate border width between 0px and 20px
								if (inputValue < 0 || inputValue > 20) {
									setBorderWidthError(true);
								} else {
									setBorderWidthError(false);
								}

								onPropertyChange("borderWidth", value);
							}}
							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
							}}
							error={borderWidthError}
								/>
								</div>
					</Box>
					{borderWidthError && (
						<Typography
							style={{
								fontSize: "12px",
								color: "#e9a971",
								textAlign: "left",
								top: "100%",
								left: 0,
								marginBottom: "5px",
								display: "flex",
							}}
						>
							<span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}
								dangerouslySetInnerHTML={{ __html: warning }}
							/>
							Value must be between 0px and 20px.
						</Typography>
					)}

                    <Box className="qadpt-control-box">
							<div className="qadpt-control-label">Primary Color</div>
							<div>
						<input
							type="color"
							value={checklistCanvasProperties.primaryColor}
							onChange={(e) => onPropertyChange("primaryColor", e.target.value)}
									className="qadpt-color-input"
									style={{backgroundColor:'#5F9EA0'}}
								/>
								</div>
                        </Box>


					<Box className="qadpt-control-box">
							<div className="qadpt-control-label">Background</div>
							<div>
						<input
							type="color"
							value={checklistCanvasProperties.backgroundColor}
							onChange={(e) => onPropertyChange("backgroundColor", e.target.value)}
							className="qadpt-color-input"
								/>
								</div>
                        </Box>


					<Box className="qadpt-control-box">
							<div className="qadpt-control-label">Border</div>
							<div>
						<input
							type="color"
							value={checklistCanvasProperties.borderColor}
							onChange={(e) => onPropertyChange("borderColor", e.target.value)}
							className="qadpt-color-input"
								/>
								</div>
					</Box>

						<Box className="qadpt-control-box">
							<div className="qadpt-control-label">Open by Default</div>
							<div>
								<label className="toggle-switch">
                                <input
                                    type="checkbox"
                                    checked={checklistCanvasProperties.openByDefault}
									onChange={(e) => onPropertyChange("openByDefault", e.target.checked)}
                                    name="showByDefault"
                                />
                                <span className="slider"></span>
								</label>
								</div>
						</Box>

                        <Box className="qadpt-control-box">
							<div className="qadpt-control-label">Hide After Completion</div>
							<div>
								<label className="toggle-switch">
                                <input
                                    type="checkbox"
                                    checked={checklistCanvasProperties.hideAfterCompletion}
									onChange={(e) => onPropertyChange("hideAfterCompletion", e.target.checked)}
                                    name="showByDefault"
                                />
                                <span className="slider"></span>
								</label>
								</div>
						</Box>


					</div>
					</div>
				<div className="qadpt-drawerFooter">
						<Button
							variant="contained"
							onClick={handleApplyChanges}
							className={`qadpt-btn ${isDisabled ? "disabled" : ""}`}
							disabled={isDisabled}
						>
							Apply
						</Button>
					</div>
			</div>

		</div>
		//</Draggable>
	);
};

export default ChecklistCanvasSettings;
