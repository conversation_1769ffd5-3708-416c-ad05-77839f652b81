import { LoginUserInfo } from './../models/LoginUserInfo';
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { User } from '../models/User';
import { Organization } from '../models/Organization';

// Define the UserInfo interface for state
interface UserInfo {
  accessToken: string;
  oidcInfo: any;
  user: User | null;
  orgDetails: Organization;
  userType: string; // added userType here
  setAccessToken: (token: string) => void;
  clearAccessToken: () => void;
  setOidcInfo: (info: any) => void;
  setUser: (user: User) => void;
  setOrgDetails: (orgDetails: Organization) => void;
    setUserType: (userType: string) => void;
    clearAll: () => void; 
}

// Zustand store with typed state
const useInfoStore = create<UserInfo>()(
  devtools(
    persist(
      immer((set) => ({
        accessToken: "",
        oidcInfo: {},
        user: {} as User,
        orgDetails: {} as Organization,
        userType: "",

        setAccessToken: (token: string) => {
          set((state) => {
            state.accessToken = token;
          });
        },

        clearAccessToken: () => {
          set((state) => {
            state.accessToken = "";
          });
        },
            
        setOidcInfo: (info: any) => {
          set((state) => {
            state.oidcInfo = info;
          });
        },

        setUser: (user: User | null) => {
          set((state) => {
            state.user = user;
          });
        },

        setOrgDetails: (orgDetails: Organization) => {
          set((state) => {
            state.orgDetails = orgDetails;
          });
        },
        clearAll: () => {  // Reset all fields to initial values
            set((state) => {
              state.accessToken = "";
              state.oidcInfo = {};
              state.user = null;
              state.orgDetails = {} as Organization;
              state.userType = "";
            });
          },
        setUserType: (userType: string) => {
          set((state) => {
            state.userType = userType;
          });
        },
      })),
      {
        name: "user-info-storage", // unique name for localStorage
        partialize: (state) => ({
          accessToken: state.accessToken,
          oidcInfo: state.oidcInfo,
          user: state.user,
          orgDetails: state.orgDetails,
          userType: state.userType,
        }), // Persist these fields
      }
    )
  )
);

export default useInfoStore;
