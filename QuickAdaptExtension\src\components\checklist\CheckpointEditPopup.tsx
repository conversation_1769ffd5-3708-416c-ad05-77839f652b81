import React, { useReducer, useState,useEffect, useRef, useContext } from "react";
import { Box, Typo<PERSON>, <PERSON>Field, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip, CircularProgress } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from "../../store/drawerStore";
import { HOTSPOT_DEFAULT_VALUE } from "../../store/drawerStore";
import {
  InfoFilled,
  QuestionFill,
  Reselect,
    Solid,
    editicon,
	deleteicon,
	chkicn1,
	chkicn2,
	chkicn3,
	chkicn4,
	chkicn5,
	chkicn6,
	deletestep,
	redirect,
	warning,
} from "../../assets/icons/icons";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import { AccountContext } from "../login/AccountContext";
import { getAllGuides } from "../../services/GuideListServices";
import InsertPhotoIcon from "@mui/icons-material/InsertPhoto";
import PersonIcon from "@mui/icons-material/Person";
import FavoriteIcon from "@mui/icons-material/Favorite";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";

const CheckPointEditPopup = ({ editInteractionName, checkpointsEditPopup }: { editInteractionName: string; checkpointsEditPopup:any}) => {
    const {
			setCheckPointsEditPopup,
			titlePopup,
			setTitlePopup,
			setDesignPopup,
			titleColor,
			setTitleColor,
			checkpointsPopup,
			setCheckPointsPopup,
			checkpointTitleColor,
			setCheckpointTitleColor,
			checkpointTitleDescription,
			setCheckpointTitleDescription,
			checkpointIconColor,
			setCheckpointIconColor,
			setUnlockCheckPointInOrder,
			unlockCheckPointInOrder,
			checkPointMessage,
			setCheckPointMessage,
			checklistGuideMetaData,
			updateChecklistCheckPointItem,
			setIsUnSavedChanges,
			isUnSavedChanges,
		} = useDrawerStore((state: any) => state);

		const data = checklistGuideMetaData[0].checkpoints.checkpointsList.find((k: any) => k.id === editInteractionName);
		const encodeToBase64 = (svgString: string) => {
			return `data:image/svg+xml;base64,${btoa(svgString)}`;
		};

		const [icons, setIcons] = useState<any[]>(() => {
			return [
				{
					id: 1,
					base64: encodeToBase64(chkicn1),
					component: (
						<span
							dangerouslySetInnerHTML={{ __html: chkicn1 }}
							style={{ zoom: 1, display: "flex" }}
						/>
					),
					selected: false,
				},
				{
					id: 2,
					base64: encodeToBase64(chkicn2),
					component: (
						<span
							dangerouslySetInnerHTML={{ __html: chkicn2 }}
							style={{ zoom: 1, display: "flex" }}
						/>
					),
					selected: false,
				},
				{
					id: 3,
					base64: encodeToBase64(chkicn3),
					component: (
						<span
							dangerouslySetInnerHTML={{ __html: chkicn3 }}
							style={{ zoom: 1, display: "flex" }}
						/>
					),
					selected: false,
				},
				{
					id: 4,
					base64: encodeToBase64(chkicn4),
					component: (
						<span
							dangerouslySetInnerHTML={{ __html: chkicn4 }}
							style={{ zoom: 1, display: "flex" }}
						/>
					),
					selected: false,
				},
				{
					id: 5,
					base64: encodeToBase64(chkicn5),
					component: (
						<span
							dangerouslySetInnerHTML={{ __html: chkicn5 }}
							style={{ zoom: 1, display: "flex" }}
						/>
					),
					selected: false,
				},
				{
					id: 6,
					base64: encodeToBase64(chkicn6),
					component: (
						<span
							dangerouslySetInnerHTML={{ __html: chkicn6 }}
							style={{ zoom: 1, display: "flex" }}
						/>
					),
					selected: false,
				},
			];
		});

		const [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState<any>(() => {
			const initialchecklistCheckpointProperties = {
				interaction: data?.interaction,
				title: data?.title,
				description: data?.description,
				redirectURL: data?.redirectURL,
				icon: data?.icon || icons[0].component,
				supportingMedia: data?.supportingMedia || [],
				mediaTitle: data?.mediaTitle,
				mediaDescription: data?.mediaDescription,
				id: data?.id,
			};
			return initialchecklistCheckpointProperties;
		});

		const handleCheckPointIconColorChange = (e: any) => setCheckpointIconColor(e.target.value);
		const handleCheckPointTitleColorChange = (e: any) => setCheckpointTitleColor(e.target.value);
		const handleCheckPointDescriptionColorChange = (e: any) => setCheckpointTitleColor(e.target.value);

		const [error, setError] = useState<string | null>(null);
		useEffect(() => {
			if (checkpointsEditPopup && checklistCheckpointProperties.icon) {
				setIcons((prevIcons) =>
					prevIcons.map((icon) => ({
						...icon,
						selected: icon.base64 === checklistCheckpointProperties.icon, // Compare Base64 strings directly
					}))
				);
			}
		}, [checkpointsEditPopup, checklistCheckpointProperties.icon]);

		const handleIconClick = (id: number) => {
			setIcons((prevIcons) =>
				prevIcons.map((icon) => ({
					...icon,
					selected: icon.id === id,
				}))
			);

			const selectedIcon = icons.find((icon) => icon.id === id);
			if (selectedIcon) {
				setChecklistCheckpointProperties((prev: any) => ({
					...prev,
					icon: selectedIcon.base64, // Save Base64 instead of component
				}));
			}
		};

		const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
			const file = event.target.files?.[0];
			if (!file) return;

			const isIco = file.name.endsWith(".ico");

			// Validate the file type and size
			const img = new Image();
			img.src = URL.createObjectURL(file);
			img.onload = () => {
				if (!isIco || img.width > 64 || img.height > 64) {
					setError("Please upload an .ico file less than 64x64px");
				} else {
					setError(null);
					setIcons((prevIcons) => [
						...prevIcons,
						{
							id: prevIcons.length + 1,
							component: (
								<img
									src={img.src}
									alt="Custom Icon"
									width={24}
								/>
							),
							selected: false,
						},
					]);
				}
			};
		};

		const handleClose = () => {
			setCheckPointsEditPopup(false);
		};
		const handledesignclose = () => {
			setDesignPopup(false);
		};
		const handleSizeChange = (value: number) => {
			const sizeInPx = 16 + (value - 1) * 4;
			onPropertyChange("Size", sizeInPx);
		};

		const onReselectElement = () => {};

		const onPropertyChange = (key: any, value: any) => {
			setChecklistCheckpointProperties((prevState: any) => ({
				...prevState,
				[key]: value,
			}));
		};

		const handleApplyChanges = () => {
			setFileError(null);

			updateChecklistCheckPointItem(checklistCheckpointProperties);
			handleClose();
			setIsUnSavedChanges(true);
		};

		const handleEditClick = () => {
			setCheckPointsEditPopup(true);
		};

		const [interactions, setInteractions] = useState<any[]>([]);

		const [skip, setSkip] = useState(0);
		const [loading, setLoading] = useState(false);
		const [hasMore, setHasMore] = useState(true);
		const dropdownRef = useRef<HTMLDivElement>(null);
		const { accountId } = useContext(AccountContext);

		// Set number of items to fetch per request to 10
		const top = 10;

		// Initial data fetch
		useEffect(() => {
			fetchData(0); // Load first 10 guides
		}, []);

		const fetchData = async (newSkip: number) => {
			if (loading || !hasMore) return; // Prevent duplicate calls or if no more data

			setLoading(true);

			const filters = [
				{
					FieldName: "AccountId",
					ElementType: "string",
					Condition: "contains",
					Value: accountId,
					IsCustomField: false,
				},
			];

			try {
				const data = await getAllGuides(newSkip, top, filters, "");
				const newInteractions = data?.results || [];

				if (newInteractions.length === 0) {
					setHasMore(false);
				} else {
					setInteractions((prev) => [...prev, ...newInteractions]);
					setSkip(newSkip + top);
				}
			} catch (error) {
				console.error("Error fetching guides:", error);
			} finally {
				setLoading(false);
			}
		};

		// Handle scroll event for the dropdown menu
		const handleMenuScroll = (event: React.UIEvent<HTMLDivElement>) => {
			const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;

			// If scrolled to bottom (with a small buffer)
			if (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore) {
				fetchData(skip);
			}
		};
		const [files, setFiles] = useState<File[]>([]);
		const [gifFile, setGifFile] = useState<File | null>(null);
		const [videoFile, setVideoFile] = useState<File | null>(null);
		const [fileError, setFileError] = useState<string | null>(null);

		const convertFileToBase64 = (file: File): Promise<string> => {
			return new Promise((resolve, reject) => {
				const reader = new FileReader();
				reader.readAsDataURL(file);
				reader.onload = () => resolve(reader.result as string);
				reader.onerror = (error) => reject(error);
			});
		};

		const convertBase64ToFile = (base64: string, fileName: string, fileType: string) => {
			if (!base64) {
				console.error("Error: Base64 string is undefined or empty.");
				return null; // Return null if invalid input
			}

			// If the base64 string doesn't contain a comma, it's probably missing the "data:image/png;base64," prefix
			const base64Data = base64.includes(",") ? base64.split(",")[1] : base64;

			try {
				const byteCharacters = atob(base64Data);
				const byteNumbers = new Array(byteCharacters.length).fill(null).map((_, i) => byteCharacters.charCodeAt(i));
				const byteArray = new Uint8Array(byteNumbers);

				return new File([byteArray], fileName, { type: fileType });
			} catch (error) {
				console.error("Error converting Base64 to File:", error);
				return null;
			}
		};
		useEffect(() => {
			if (checkpointsEditPopup && checklistCheckpointProperties.supportingMedia?.length > 0) {
				const mediaFiles = checklistCheckpointProperties.supportingMedia
					.map((media: any) => {
						if (typeof media === "string") {
							return null; // Skip if media is a plain string (unexpected case)
						} else if (typeof media === "object" && media.Base64) {
							return convertBase64ToFile(media.Base64, media.Name, media.Type); // ✅ Use actual file name
						}
						return null;
					})
					.filter((file: any): file is File => file !== null); // ✅ Remove null values

				// ✅ Separate files correctly
				const imageFiles = mediaFiles.filter((file: any) =>
					["image/jpeg", "image/png", "image/jpg"].includes(file.type)
				);
				const gif = mediaFiles.find((file: any) => file.type === "image/gif") || null;
				const video = mediaFiles.find((file: any) => file.type === "video/mp4") || null;

				setFiles(imageFiles);
				setGifFile(gif);
				setVideoFile(video);
			}
		}, [checkpointsEditPopup, checklistCheckpointProperties.supportingMedia]);

		const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
			setFileError(null);
			if (!event.target.files) return;

			const newFiles = Array.from(event.target.files);

			const base64Files = await Promise.all(
				newFiles.map(async (file) => ({
					Name: file.name,
					Type: file.type,
					Base64: await convertFileToBase64(file),
				}))
			);

			const fileObjects = base64Files
				.map((fileData) => convertBase64ToFile(fileData.Base64, fileData.Name, fileData.Type))
				.filter((file): file is File => file !== null);

			const newGifs = fileObjects.filter((file) => file.name.toLowerCase().endsWith(".gif"));
			const newVideos = fileObjects.filter((file) => file.name.toLowerCase().endsWith(".mp4"));
			const newImages = fileObjects.filter((file) =>
				[".png", ".jpg", ".jpeg"].some((ext) => file.name.toLowerCase().endsWith(ext))
			);

			// Validate types
			const allTypes = Array.from(new Set(fileObjects.map((file) => file.type)));
			if (allTypes.length > 1) {
				setFileError("Mixed file formats are not allowed.");
				return;
			}

			// Case 1: Check if a GIF already exists
			if (gifFile && newGifs.length > 0) {
				setFileError("Only one GIF allowed");
				return;
			}

			// Case 2: Check if a video already exists
			if (videoFile && newVideos.length > 0) {
				setFileError("Only one Video allowed");
				return;
			}

			// Case 3: If a GIF exists, prevent uploading any other type
			if (gifFile && (newVideos.length > 0 || newImages.length > 0)) {
				setFileError("Mixed file formats are not allowed.");
				return;
			}

			// Case 4: If a video exists, prevent uploading any other type
			if (videoFile && (newGifs.length > 0 || newImages.length > 0)) {
				setFileError("Mixed file formats are not allowed.");
				return;
			}

			// Case 5: If images exist, ensure new images are same type
			if (files.length > 0 && newImages.length > 0) {
				const existingType = files[0].type;
				const newImageType = newImages[0].type;

				const isSameType = newImages.every((img) => img.type === existingType);
				if (!isSameType || newImageType !== existingType) {
					setFileError("Mixed file formats are not allowed.");
					return;
				}
			}

			// Case 6: If images exist and GIF/MP4 is being added
			if (files.length > 0 && (newGifs.length > 0 || newVideos.length > 0)) {
				setFileError("Mixed file formats are not allowed.");
				return;
			}

			// Set accepted files
			if (newGifs.length > 0) {
				setGifFile(newGifs[0]);
			}
			if (newVideos.length > 0) {
				setVideoFile(newVideos[0]);
			}
			if (newImages.length > 0) {
				setFiles((prevFiles) => {
					const updated = [...prevFiles, ...newImages];
					updated.sort((a, b) => {
						const aNum = a.name.match(/\d+/) ? parseInt(a.name.match(/\d+/)![0], 10) : 0;
						const bNum = b.name.match(/\d+/) ? parseInt(b.name.match(/\d+/)![0], 10) : 0;
						return aNum - bNum;
					});
					return updated;
				});
			}

			// Update media
			setChecklistCheckpointProperties((prevState: any) => {
				const updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];
				updatedMedia.sort((a, b) => {
					const aNum = a.Name.match(/\d+/) ? parseInt(a.Name.match(/\d+/)![0], 10) : 0;
					const bNum = b.Name.match(/\d+/) ? parseInt(b.Name.match(/\d+/)![0], 10) : 0;
					return aNum - bNum;
				});
				return { ...prevState, supportingMedia: updatedMedia };
			});
		};

		const handleDeleteFile = (index: number) => {
			setFileError(null);
			setFiles((prevFiles) => {
				const updated = prevFiles.filter((_, i) => i !== index);
				setChecklistCheckpointProperties((prev: any) => ({
					...prev,
					supportingMedia: prev.supportingMedia?.filter((_: any, i: any) => i !== index) || [],
				}));
				return updated;
			});
		};

		const handleDeleteGif = () => {
			setGifFile(null);
			setChecklistCheckpointProperties((prev: any) => ({
				...prev,
				supportingMedia: prev.supportingMedia?.filter((file: any) => !file.Name?.toLowerCase().endsWith(".gif")) || [],
			}));
		};

		const handleDeleteVideo = () => {
			setVideoFile(null);
			setChecklistCheckpointProperties((prev: any) => ({
				...prev,
				supportingMedia: prev.supportingMedia?.filter((file: any) => !file.Name?.toLowerCase().endsWith(".mp4")) || [],
			}));
		};

		const index = checklistGuideMetaData[0]?.checkpoints?.checkpointsList?.findIndex(
			(i: any) => i.id === editInteractionName
		);

		return (
			<div
				id="qadpt-designpopup"
				className="qadpt-designpopup"
			>
				<div className="qadpt-content">
					<div className="qadpt-design-header">
						<IconButton
							aria-label="back"
							onClick={handleClose}
						>
							<ArrowBackIosNewOutlinedIcon />
						</IconButton>
						<div className="qadpt-title">Step {index + 1}</div>
						<IconButton
							size="small"
							aria-label="close"
							onClick={handleClose}
						>
							<CloseIcon />
						</IconButton>
					</div>

					<div className="qadpt-canblock">
						<div className="qadpt-controls">
							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "0 !important" }}
							>
								<div className="qadpt-control-label">Interaction</div>

								{/* Disabled Select-Like Display */}
								<FormControl
									variant="outlined"
									fullWidth
									className="qadpt-control-input"
									sx={{
										width: "calc(100% - 13px) !important",
										borderRadius: "12px",
										padding: "0 8px 8px 8px",
										margin: "0 !important",
									}}
								>
									<Select
										displayEmpty
										disabled
										value={checklistCheckpointProperties.interaction || ""}
										sx={{
											width: "100% !important",
											borderRadius: "12px",
											backgroundColor: "#f5f5f5",
											padding: "10px",
											color: "#333",
											".MuiSelect-icon": {
												display: "none", // Hide the dropdown arrow to keep the read-only feel
											},
											"& .MuiOutlinedInput-root": {
												"&:hover": {
													borderColor: "none !important",
												},
												"&.Mui-focused": {
													borderColor: "none !important",
												},
											},
											"& .MuiOutlinedInput-notchedOutline": {
												border : "none !important"
											},
											"&.MuiInputBase-root": { height: "35px !important" }
										}}
									>
										<MenuItem value={checklistCheckpointProperties.interaction}>
											{checklistCheckpointProperties.interaction || "No Interaction Selected"}
										</MenuItem>
									</Select>
								</FormControl>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "8px !important" }}
							>
								<Typography
									className="qadpt-control-label"
									sx={{ padding: "0 !important", marginBottom: "8px !important" }}
								>
									Title
								</Typography>

								<TextField
									variant="outlined"
									size="small"
									placeholder="Step Title"
									className="qadpt-control-input"
									style={{ width: "100%" }}
									value={checklistCheckpointProperties.title}
									onChange={(e) => onPropertyChange("title", e.target.value)}
									InputProps={{
										endAdornment: "",
										sx: {
											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset": { border: "none" },
											"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
											"&.MuiInputBase-root": { height: "auto !important" },
										},
									}}
								/>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "8px !important" }}
							>
								<Typography
									className="qadpt-control-label"
									sx={{ padding: "0 !important", marginBottom: "8px !important" }}
								>
									Description
								</Typography>

								<TextField
									variant="outlined"
									size="small"
									placeholder="Step Desc"
									className="qadpt-control-input"
									multiline
									minRows={3}
									style={{ width: "100%" }}
									value={checklistCheckpointProperties.description}
									onChange={(e) => onPropertyChange("description", e.target.value)}
									InputProps={{
										endAdornment: "",
										sx: {
											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset": { border: "none" },
											"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
											"&.MuiInputBase-root": { height: "auto !important" },
										},
									}}
								/>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "8px !important" }}
							>
								<div
									className="qadpt-control-label"
									style={{
										display: "flex",
										flexDirection: "row",
										alignItems: "center",
										gap: "5px",
										padding: "0",
										marginBottom: "10px",
									}}
								>
									<Typography sx={{ color: "#444444", fontWeight: "600" }}>Redirect URL</Typography>
									<span
										dangerouslySetInnerHTML={{ __html: redirect }}
										style={{ display: "flex" }}
									/>{" "}
								</div>

								<Typography
									style={{ fontSize: "11px", color: "#8d8d8d", textAlign: "left", padding: "0", marginBottom: "10px" }}
								>
									User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips
								</Typography>
								<TextField
									variant="outlined"
									size="small"
									placeholder="Redirection URL"
									className="qadpt-control-input"
									style={{ width: "100%" }}
									value={checklistCheckpointProperties?.redirectURL}
									onChange={(e) => onPropertyChange("redirectURL", e.target.value)}
									InputProps={{
										endAdornment: "",
										sx: {
											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset": { border: "none" },
											"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
											"&.MuiInputBase-root": { height: "auto !important" },
										},
									}}
								/>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "0 !important" }}
							>
								<Typography className="qadpt-control-label">Icon</Typography>
								<Box
									sx={{
										display: "flex",
										gap: 1,
										alignItems: "center",
										width: "-webkit-fill-available",
										flexWrap: "wrap",
										padding: "0 8px 8px 8px",
									}}
								>
									{icons.map((icon) => (
										<Tooltip
											key={icon.id}
											title="Select Icon"
										>
											<IconButton
												onClick={() => handleIconClick(icon.id)}
												sx={{
													border: icon.selected ? "2px solid var(--primarycolor)" : "none",
													borderRadius: "8px",
													padding: "8px",
													background: "#F1ECEC",
												}}
											>
												{icon.component}
											</IconButton>
										</Tooltip>
									))}
								</Box>
							</Box>

							{/* <Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "0 !important" }}
							>
								<Typography className="qadpt-control-label">Icon Background Color</Typography>
								<Box sx={{ padding: "0 8px 8px 8px" }}>
									<input
										type="color"
										value={
											checklistCheckpointProperties.iconBackgroundColor ||
											checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons ||
											"#333"
										}
										onChange={(e) => {
											onPropertyChange("iconBackgroundColor", e.target.value);
										}}
										className="qadpt-color-input"
										style={{ width: "100%", height: "30px" }}
									/>
								</Box>
							</Box> */}

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "0 !important" }}
							>
								<Typography className="qadpt-control-label">Supporting Media</Typography>

								<div
									style={{
										width: "165px",
										height: "auto",
										margin: "0 8px 8px 8px",
										display: "flex",
										flexDirection: "column",
										alignItems: "center",
										justifyContent: "center",
										border: "1px dashed var(--primarycolor)",
										borderRadius: "12px",
										padding: "8px",
										background: "#F1ECEC",
										textAlign: "center",
									}}
								>
									<Button
										className="qadpt-upload-button"
										style={{
											height: "auto",
											padding: "0",
											width: "100%",
											display: "flex",
											flexDirection: "row", // Ensures icon & text are in one line
											alignItems: "center",
											justifyContent: "center",
											gap: "6px",
											color: "#000",
											backgroundColor: "#F1ECEC",
											textTransform: "capitalize",
											boxShadow: "none",
										}}
										variant="contained"
										component="label"
									>
										<CloudUploadOutlinedIcon sx={{ zoom: "1.6" }} />
										Upload File
										<input
											id="file-input"
											type="file"
											multiple
											accept=".jpeg, .jpg, .png, .gif, .mp4" // ✅ Added MP4 support
											onChange={handleFileChange}
											style={{ display: "none" }}
										/>
									</Button>
									<Typography style={{ fontSize: "12px", color: "#A3A3A3" }}>.png, .jpg, .gif, .mp4</Typography>
								</div>
								{fileError && (
									<div
										style={{
											display: "flex",
											alignItems: "center",
											color: "#e6a957",
											width: "-webkit-fill-available",
											padding: "0 8px",
											textAlign: "left",
										}}
									>
										<span
											style={{ marginRight: "4px", display: "flex" }}
											dangerouslySetInnerHTML={{ __html: warning }}
										/>
										<div style={{ fontSize: "12px" }}>{fileError}</div>
									</div>
								)}

								{/* Display uploaded images */}
								<Box sx={{ width: "-webkit-fill-available" }}>
									{" "}
									{files.map((file, index) => (
										<Box
											key={index}
											display="flex"
											alignItems="center"
											justifyContent="space-between"
											sx={{
												borderRadius: "12px",
												padding: "8px",
												margin: "8px",
												backgroundColor: "#e5dada",
											}}
										>
											<img
												src={URL.createObjectURL(file)}
												alt={`uploaded-${index}`}
												style={{ width: "20px", height: "20px", borderRadius: "5px" }}
											/>
											{/* //<span dangerouslySetInnerHTML={{ __html: imageIcon }} style={{ zoom: 0.7 }} /> */}
											<Typography sx={{ flex: 1, ml: 2, fontSize: "14px", wordBreak: "break-word" }}>
												{file.name}
											</Typography>
											<IconButton
												onClick={() => handleDeleteFile(index)}
												size="small"
											>
												<span
													dangerouslySetInnerHTML={{ __html: deletestep }}
													style={{ zoom: "1", display: "flex" }}
												/>{" "}
											</IconButton>
										</Box>
									))}
									{/* Display uploaded GIF separately */}
									{gifFile && (
										<Box
											display="flex"
											alignItems="center"
											justifyContent="space-between"
											sx={{
												borderRadius: "12px",
												padding: "8px",
												margin: "5px",
												backgroundColor: "#e5dada",
											}}
										>
											<img
												src={URL.createObjectURL(gifFile)}
												alt="uploaded-gif"
												style={{ width: "20px", height: "20px", borderRadius: "5px" }}
											/>
											<Typography sx={{ flex: 1, fontSize: "14px", wordBreak: "break-word" }}>
												{gifFile.name}
											</Typography>
											<IconButton
												onClick={handleDeleteGif}
												size="small"
											>
												<span
													dangerouslySetInnerHTML={{ __html: deletestep }}
													style={{ zoom: "1", display: "flex" }}
												/>{" "}
											</IconButton>
										</Box>
									)}
									{videoFile && (
										<Box
											display="flex"
											alignItems="center"
											justifyContent="space-between"
											sx={{
												border: "1px solid #0a6",
												borderRadius: "5px",
												padding: "8px",
												marginBottom: "5px",
												width: "196px",
												backgroundColor: "#e6ffe6",
											}}
										>
											<video
												width="40"
												height="40"
												controls
											>
												<source
													src={URL.createObjectURL(videoFile)}
													type="video/mp4"
												/>
												Your browser does not support the video tag.
											</video>
											<Typography sx={{ flex: 1, ml: 2, fontSize: "14px", wordBreak: "break-word" }}>
												{videoFile.name}
											</Typography>
											<IconButton
												onClick={handleDeleteVideo}
												size="small"
											>
												<span
													dangerouslySetInnerHTML={{ __html: deleteicon }}
													style={{ zoom: 0.7 }}
												/>
											</IconButton>
										</Box>
									)}
								</Box>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "8px !important" }}
							>
								<Typography
									className="qadpt-control-label"
									sx={{ padding: "0 !important", marginBottom: "8px !important" }}
								>
									Media Title
								</Typography>

								<TextField
									variant="outlined"
									size="small"
									placeholder="Media Title"
									className="qadpt-control-input"
									style={{ width: "100%" }}
									value={checklistCheckpointProperties.mediaTitle}
									onChange={(e) => onPropertyChange("mediaTitle", e.target.value)}
									//onChange={(e) => onPropertyChange("XPosition", e.target.value)}
									InputProps={{
										endAdornment: "",
										sx: {
											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset": { border: "none" },
											"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
											"&.MuiInputBase-root": { height: "auto !important" },
										},
									}}
								/>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", padding: "8px !important" }}
							>
								<Typography
									className="qadpt-control-label"
									sx={{ padding: "0 !important", marginBottom: "8px !important" }}
								>
									Media Description
								</Typography>

								<TextField
									variant="outlined"
									size="small"
									placeholder="Media Desc"
									className="qadpt-control-input"
									multiline
									minRows={3}
									style={{ width: "100%" }}
									value={checklistCheckpointProperties.mediaDescription}
									onChange={(e) => {
										let value = e.target.value;
										if (value.length > 200) {
											value = value.slice(0, 200);
										}
										onPropertyChange("mediaDescription", value);
									}}
									helperText={`${checklistCheckpointProperties.mediaDescription?.length || 0}/200`}
									InputProps={{
										endAdornment: "",
										sx: {
											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset": { border: "none" },
											"& input": { textAlign: "left !important", paddingLeft: "10px !important" },
											"&.MuiInputBase-root": { height: "auto !important" },
										},
									}}
								/>
							</Box>
						</div>
					</div>

					<div className="qadpt-drawerFooter">
						<Button
							variant="contained"
							onClick={handleApplyChanges}
							className="qadpt-btn"
						>
							Apply
						</Button>
					</div>
				</div>
			</div>
		);
};

export default CheckPointEditPopup;
