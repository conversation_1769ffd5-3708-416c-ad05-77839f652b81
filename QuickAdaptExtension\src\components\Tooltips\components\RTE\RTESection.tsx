import React, { useState, useEffect, forwardRef, useRef, RefObject, memo, useMemo } from "react";
import { Box, Popover, Tooltip, Typography, IconButton } from "@mui/material";

import RTE from "./RTE";
import useDrawerStore, { IRTEContainer, TSectionType } from "../../../../store/drawerStore";
import { Code, GifBox, Image, Link, TextFormat, VideoLibrary } from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import { copyicon, deleteicon } from "../../../../assets/icons/icons";
interface RTEsectionProps {
	items: IRTEContainer;
	boxRef: React.RefObject<HTMLDivElement>;
	handleFocus: (id: string) => void;
	handleeBlur: (id: string) => void;

	isPopoverOpen: boolean;
	setIsPopoverOpen: (params: boolean) => void;
	currentRTEFocusedId: string;
}

const RTEsection: React.FC<RTEsectionProps> = forwardRef(
	(
		{
			items: { id, style, rteBoxValue, placeholder },
			boxRef,
			handleFocus,
			handleeBlur,

			isPopoverOpen,
			setIsPopoverOpen,
			currentRTEFocusedId,
		},
		ref
	) => {
		const {
			setIsUnSavedChanges,
			setHtmlContent,
			textvaluess,
			setTextvaluess,
			backgroundC,
			setBackgroundC,
			Bbordercolor,
			BborderSize,
			bpadding,
			sectionColor,
			setSectionColor,
			handleTooltipRTEBlur,
			handleTooltipRTEValue,
			handleRTEDeleteSection,
			handleRTECloneSection,
			tooltip,
			currentStep,
			toolTipGuideMetaData,
		} = useDrawerStore((state) => state);
		const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
		const [savedRange, setSaveRange] = useState<Range | undefined>(undefined);
		const [anchorPosition, setAnchorPosition] = useState<{ top: number; left: number }>({ top: 300, left: 700 });
		const [isPlaceholderVisible, setIsPlaceholderVisible] = useState(true);
		const [isEditing, setIsEditing] = useState(false);
		const editorRef = useRef(null);
		const containerRef = useRef<HTMLDivElement | null>(null);

		// const handleInput = () => {
		// 	// Update the content state when user types
		// 	if (boxRef.current) {
		// 		const updatedContent = boxRef.current.innerHTML;
		// 		setContent(updatedContent); // Store the content in state
		// 		setHtmlContent(updatedContent); // Update the HTML content
		// 		setIsUnSavedChanges(true);
		// 		preserveCaretPosition();
		// 	}
		// };
		const preserveCaretPosition = () => {
			const selection = document.getSelection();
			if (selection) {
				const range = selection.getRangeAt(0); // Get the current range (cursor position)
				setSaveRange(range); // Save the current range for later restoration
			}
		};

		const restoreCaretPosition = () => {
			if (savedRange && boxRef.current) {
				const selection = document.getSelection();
				if (selection) {
					selection.removeAllRanges();
					selection.addRange(savedRange); // Restore the saved range
				}
			}
		};

		// useEffect(() => {
		// 	// After content update, restore the cursor position
		// 	restoreCaretPosition();
		// }, [boxRef.current?.innerHTML]); // Run when content changes

		// Remove section

		// useEffect(() => {
		// 	if (boxRef.current?.innerHTML?.trim()) {
		// 		setIsUnSavedChanges(true);
		// 	}
		// }, [boxRef.current?.innerHTML?.trim()]);

		useEffect(() => {
			if (rteBoxValue && boxRef.current?.innerHTML && boxRef.current?.innerHTML !== "<p><br></p>") {
				// @ts-ignore
				boxRef.current.innerHTML = rteBoxValue;
				setIsPlaceholderVisible(false);
			} else if (!textvaluess && boxRef.current?.innerHTML) {
				// @ts-ignore
				boxRef.current.innerHTML = "";
				setIsPlaceholderVisible(true);
			}
		}, [rteBoxValue, boxRef.current]);

		// Auto-focus the editor when editing mode is activated
		useEffect(() => {
			if (isEditing && editorRef.current) {
				setTimeout(() => {
					(editorRef.current as any).editor.focus();
				}, 50);
			}
		}, [isEditing]);

		// Handle clicks outside the editor to close editing mode
		useEffect(() => {
			const handleClickOutside = (event: MouseEvent) => {
				const isInsideJoditPopupContent = (event.target as HTMLElement).closest(".jodit-popup__content") !== null;
				const isInsideAltTextPopup = (event.target as HTMLElement).closest(".jodit-ui-input") !== null;
				const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
				const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
				const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(".jodit-dialog__panel")?.contains(event.target as Node);
				const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
				const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
				const isInsideToolbarButton = (event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
				const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;

				// Check if the target is inside the editor or related elements
				if (
					containerRef.current &&
					!containerRef.current.contains(event.target as Node) && // Click outside the editor container
					!isInsidePopup && // Click outside the popup
					!isInsideJoditPopup && // Click outside the WYSIWYG editor
					!isInsideWorkplacePopup && // Click outside the workplace popup
					!isSelectionMarker && // Click outside selection markers
					!isLinkPopup && // Click outside link input popup
					!isInsideToolbarButton &&// Click outside the toolbar button
					!isInsertButton &&
					!isInsideJoditPopupContent &&
					!isInsideAltTextPopup
				) {
					setIsEditing(false); // Close the editor if clicked outside
				}
			};

			if (isEditing) {
				document.addEventListener("mousedown", handleClickOutside);
				return () => document.removeEventListener("mousedown", handleClickOutside);
			}
		}, [isEditing]);

		return (
			<>
				<Box
					sx={{
						display: "flex",
						alignItems: "center",
						position: "relative",
						//padding: 0,
						margin: 0,
						boxSizing: "border-box",
						transition: "border 0.2s ease-in-out",
						backgroundColor: sectionColor || "defaultColor",
						//border: `${BborderSize}px solid ${Bbordercolor} !important` || "defaultColor",
						// padding: `${bpadding}px !important` || "0",
					}}
					className="qadpt-rte"
					id="rte-box"
				>
					<Tooltip
						title={
							<>
<IconButton
	size="small"
	onClick={() => handleRTEDeleteSection(id)}
	disabled={
		toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1
	}
	sx={{
	"&:hover": {
		backgroundColor: "transparent !important",
		},
		svg: {
			path: {
				fill:"var(--primarycolor)"
			}
		},
	}}
>
	<span
		dangerouslySetInnerHTML={{ __html: deleteicon }}
		style={{
			opacity:
				toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1
					? 0.5
					: 1,
			pointerEvents: 'none',
			height: "24px",
		}}
	/>
</IconButton>

								<IconButton
									size="small"
									onClick={() => handleRTECloneSection(id)}
									sx={{
										"&:hover": {
											backgroundColor: "transparent !important",
										},
										svg: {
											height: "24px",
											path: {
												fill:"var(--primarycolor)"
											}
										},
										}}
								>
									<span dangerouslySetInnerHTML={{ __html: copyicon }}
									style={{
										opacity:
											toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1
												? 0.5
												: 1,
										pointerEvents: 'none',
										height: "24px",
									}}/>
								</IconButton>
							</>
						}
						placement="top"
						slotProps={{
							tooltip: {
								sx: {
									backgroundColor: "white",
									color: "black",
									borderRadius: "4px",
									padding: '0px 4px',
									border: "1px dashed var(--primarycolor)",
								},
							},
						}}
						PopperProps={{
							modifiers: [
								{
									name: "preventOverflow",
									options: {
										boundary: "viewport", // Ensure tooltip doesn't go outside the viewport
									},
								},
								{
									name: "flip",
									options: {
										enabled: true,
									},
								},
							],
						}}
					>
						<Box
							contentEditable
							ref={boxRef}
							component={"div"}
							id={`rt-editor${id}`}
							onClick={(e) => {
								// Immediately activate editing mode and focus on first click
								handleFocus(id);
								setIsPlaceholderVisible(false);
								// Focus the contentEditable element to make it ready for typing
								setTimeout(() => {
									if (boxRef.current) {
										boxRef.current.focus();
									}
								}, 0);
							}}
							onFocus={(e) => {
								handleFocus(id);
								setIsPlaceholderVisible(false);
							}}
							onBlur={(e) => {
								handleeBlur(id);
								if (!boxRef.current?.innerHTML?.trim()) {
									setIsPlaceholderVisible(true);
								}
							}}
							//onInput={handleInput}
							sx={{
								height: "100%",
								width: "100%",
								padding: "8px",
								outline: "none",
								overflowY: "auto",
								color: isPlaceholderVisible && !boxRef.current?.innerHTML?.trim() ? "transparent" : "black",
								position: "relative",
								cursor: "text", // Add cursor pointer to indicate it's clickable
								//backgroundColor: backgroundC || "defaultColor",
							}}
							suppressContentEditableWarning={true}
							// Set content via state using dangerouslySetInnerHTML
							dangerouslySetInnerHTML={{ __html: rteBoxValue }}
						/>
					</Tooltip>

					{isPlaceholderVisible && !boxRef.current?.innerHTML?.trim() && (
						<span
							style={{
								position: "absolute",
								color: "gray",
								pointerEvents: "none",
								userSelect: "none",
								textAlign: "center",
								whiteSpace: "nowrap",
								transform: "translate(-50%,-50%)",
								top: "50%",
								left: "50%",
								padding: "8px",
								// outline: "none",
							}}
							id="rte-placeholder"
						>
							{placeholder}
						</span>
					)}
				</Box>
			</>
		);
	}
);

export default memo(RTEsection);
