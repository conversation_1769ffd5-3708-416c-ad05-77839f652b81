@import url('https://fonts.googleapis.com/css2?family=Syncopate:wght@700&display=swap');
/* @import '../../components/guideDesign/Canvas.module.css'; */
:root {
	--font-family: Gotham Poppins, Proxima Nova, arial, serif;
    --primarycolor: #5F9EA0;
    --ext-background: #F6EEEE;
    --border-color: #ccc;
    --white-color: #fff;
    --back-light-color:#EAE2E2;
    --font-size : 14px;
    --button-padding : 4px 8px;
    --button-lineheight : normal;
    --error-color:#d05353;
}
*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon) {
	font-family: var(--font-family) !important;
}
*:not(.qadpt-rte *):not(.qadptWelcomeMessage):not(.previewdata *):not(.qadpt-previewdata *):not(.qadpt-stpdrp *):not(.qadpt-nmuq *):not(.qadpt-jodit *)

{
    font-size: var(--font-size);
}
body {
	margin: 0;
	font-family: var(--font-family) !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

@font-face {
	font-family: "Gotham Pro";
	font-style: normal;
	/* src: local("Gotham Pro"), local("Gotham Pro"), url("../../assets/fonts/GothamPro.woff2") format("woff2"),
		url("../../assets/fonts/GothamPro.woff") format("woff"); */
}

@font-face {
	font-family: "Proxima Nova";
	font-style: normal;
	src: local("Proxima Nova"), local("ProximaNova-Regular"),
		url("../../assets/fonts/ProximaNova-Regular.woff2") format("woff2"),
		url("../../assets/fonts/ProximaNova-Regular.woff") format("woff");
}

@font-face {
	font-family: "qadapt-icons";
	src: url("../../assets/fonts/qadapt-icons.eot?qmcsfb");
	src: url("../../assets/fonts/qadapt-icons.eot?qmcsfb#iefix") format("embedded-opentype"),
		url("../../assets/fonts/qadapt-icons.ttf?qmcsfb") format("truetype"),
		url("../../assets/fonts/qadapt-icons.woff?qmcsfb") format("woff"),
		url("../../assets/fonts/qadapt-icons.svg?qmcsfb#qadapt-icons") format("svg");
	font-weight: normal;
	font-style: normal;
	font-display: block;
}

.leftDrawer {
    position: fixed;
    left: 0;
    top: 0;
    width: 300px !important;
    min-width: 270px !important;
    height: 100%;
    background-color: var(--white-color);
    border-right: 1px solid var(--primarycolor);
    transition: width 0.3s ease, background-color 0.3s ease !important;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    z-index: 99999 !important;
    overflow: hidden !important;
}
.leftDrawer.closed {
    display: none;
  }
  .leftDrawer .scrollbar-container{
    padding: 0px 20px 0px 20px;
}
.leftDrawer.collapsed {
    width: 56px !important;
    min-width: 35px !important;
    background-color: var(--primarycolor) !important;
    padding: 20px 0px;
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
}
.leftDrawer.collapsed .qadpt-drawerHeader{
    display: block !important;
    padding: 0 !important;
}

.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item{
		padding: 0 !important;
        margin: 20px 10px !important;
        border: none !important;
        box-shadow: none !important;
        border-radius: 50px;
        height: 38px;
        width: 38px;
        display: flex;
        align-items: center;
        place-content: center;
}
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item.active {
    background-color: white !important;
  }
  /* .leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item:hover {
    border: 2px solid white !important;
}
   */
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .menu-content:hover {
    border: 2px solid white !important;
    border-radius: 50%;
    padding: 5px;

}
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .icons{
    margin-right: 0 !important;
    display: block !important;
    /* height: 30px !important;
    width: 30px !important; */

}
/* .leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item:hover .icons svg {
    transform: scale(1.4);
    transition: transform 0.2s ease-in-out;
} */
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .icons svg{
    height: 22px !important;
    width: 22px !important;
    fill: #fff;
    stroke: #fff;
    display: flex;
}

.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item .icons .qadpt-colsvg path{
    fill: #fff ;
    stroke:#fff ;
}
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .menu-list .menu-item.active .icons .qadpt-colsvg{
    svg,
    path{
        fill: var(--primarycolor) !important;
        stroke: var(--primarycolor) !important;
    }
}
.leftDrawer.collapsed .qadpt-drawerHeader .qadpt-ai-button{
    svg,
    path{
        fill: #fff ;
    stroke:#fff ;
    }
}


.leftDrawer.collapsed .qadpt-drawerHeader .qadpt-toggleIcon{
    /* filter: brightness(0.5);*/
    display: flex;
    align-items: center;
    place-content: center;
    width: 60px !important;
}
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .scrollbar-container{
    padding: 0 !important;
}
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button{
    padding: 0 !important;
    margin: 20px 10px !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 50px;
    height: 38px;
    width: 38px;
    display: flex;
    align-items: center;
    place-content: center;
    cursor: pointer;
    background: none;
    outline: none;
}
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button .qadpt-aiicon{
    display: flex;
}

.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button.active{
    background: linear-gradient(360deg, #04417F 0%, #0776E5 100%) !important;
}


.qadpt-selectaccount {
    width: 115px !important;
    height: 33px !important;
}

.qadpt-drawerHeader {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    padding: 20px 0px 0px 10px;
}
.qadpt-drawerHeader .qadpt-toggleIcon {
    width: 30px;
    cursor: pointer;
    top: 0;
    position: relative;
}
/* .qadpt-drawerHeader .qadpt-toggleIcon svg{
    filter: brightness(0.5);
} */
.grid-toolbar-options {
	width: 100% !important;
	margin: 20px 0 0;
	.left-options {
		width: 100%;
		max-width: 100%;
		display: flex;
		.drp-fields,
		.dt-fields {
			display: flex;
			margin-right: 15px;
			.auto-filed,
			.qadpt-DateTime {
				width: 50%;
				max-width: 50%;
				min-width: 50%;
				margin: 0 5px;
			}
			button {
				text-transform: unset !important;
			}
		}
		.drp-fields {
			.MuiInputBase-root.MuiOutlinedInput-root {
				padding-right: 35px;
				.MuiAutocomplete-input {
					width: inherit !important;
					min-width: 30px;
					max-width: 100%;
				}
			}
		}
		.auto-filed,
		.name-fld,
		.qadpt-DateTime {
			.MuiSvgIcon-root {
				height: 16px !important;
				width: 16px !important;
			}
			&.dt-fld2 {
				margin-left: -26px;
				&.hide-close {
					margin-left: 0;
				}
			}
			input {
				font-size: 12px !important;
			}
		}
		.dt-close-icon {
			right: 55px;
			height: 40px;
		}
	}

	.right-options {
		display: flex;
		margin: 5px;
		place-content: flex-end;
		button {
			text-transform: unset !important;
			padding: 5px;
			height: 32px;
			border-radius: 20px;
			margin: 0 0 0 5px;
		}
	}
	svg {
		height: 16px;
		width: 16px;
	}
}
/* .qadpt-drawerHeader .qadpt-leftdrawer.collapsed .qadpt-toggleIcon {
    left: 3px;
    margin-top: 40px;
} */
.qadpt-drawerHeader .qadpt-drawerTitle {
    font-family: "Syncopate", sans-serif !important;
    font-size: 14px;
    font-weight: 700;
    color: var(--primarycolor);
    width:calc(100% - 80px);
}
.qadpt-drawerHeader .qadpt-threeDotMenu {
    position: relative;
    cursor: pointer;
    min-width: 40px;
}
.qadpt-drawerHeader .qadpt-threeDotMenu svg{
    filter: brightness(0.5);
}

/* #newInteractionBtn:hover {
    background-color: #34A080;
}
*/
.qadpt-drawerContent {
    width: 100%;
    margin-top: 20px;
}
.qadpt-drawerContent .qadpt-welcome-message {
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    padding: 13px;
}
.qadpt-drawerContent .qadpt-login-form {
    margin-top: 20px;
}
.qadpt-drawerContent .qadpt-login-form .qadpt-form-label {
    font-size: 14px;
    margin-top: 10px;
    text-align: left;
}
.qadpt-drawerContent .qadpt-login-form .qadpt-input-field {
    font-size: 16px;
    font-weight: 400;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: none;
    height: 46px;
    background-color: var(--white-color);
    margin-top: 10px;
}
.qadpt-drawerContent .qadpt-login-form .qadpt-invalidcreds {
    font-size: 14px;
}
.qadpt-drawerContent .qadpt-login-form .qadpt-forgotpwd {
    color: var(--primarycolor) !important;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin-top: 10px;
    text-align: left;
}
.qadpt-drawerContent .qadpt-subhead {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-left: 10px;
}
.qadpt-drawerContent .qadpt-subhead .qadpt-backbtn {
    position: absolute;
    left: 10px;
    cursor: pointer;
    top: 4px;
}
.qadpt-drawerContent .qadpt-subhead .qadpt-subhead-title {
    font-size: 18px !important;
    font-weight: 600;
}
.qadpt-drawerContent .qadpt-divider {
    margin-top: 10px;
}
.qadpt-drawerContent .qadpt-items {
    display: grid;
    gap: 10px;
    margin-top: 20px;
    margin-left: -4px;
}
.qadpt-drawerContent .qadpt-items .qadpt-subitem-img {
    width: 130px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
}
.qadpt-drawerContent .qadpt-items .qadpt-subitem-img.selected {
    background: linear-gradient(270deg, #ededed 0%, rgba(95, 158, 160, 0.5) 100%);
}
.qadpt-drawerContent .qadpt-items .qadpt-subitem-img:not(.selected) {
    background: #eae2e2;
}
.qadpt-drawerContent .qadpt-items .qadpt-item-label {
    font-size: 14px;
    font-weight: 600;
    text-align: left;
}
.qadpt-drawerContent .qadpt-guide-form {
    margin-top: 20px;
    padding: 0 16px;
}
.qadpt-drawerContent .qadpt-guide-form .MuiFormHelperText-root.Mui-error {
    color: #e6a957 !important;
}

.qadpt-drawerContent .qadpt-guide-form .qadpt-guide-label {
    font-size: 14px;
    font-weight: 600;
    color: #444;
    margin-bottom: 5px;
    text-align: left;
}
.qadpt-drawerContent .qadpt-guide-form .qadpt-guide-input {
    height: 45px;
    padding: 12px;
    gap: 10px;
    border-radius: 4px;
    opacity: 1;
    margin-bottom: 15px;

}
.qadpt-drawerContent .qadpt-guide-form .qadpt-guide-input input{
    height: auto !important;
    line-height: initial !important;
    background: initial !important;
    padding: 5px;
    font-size: 14px !important;
}
.qadpt-drawerContent .qadpt-guide-form p.Mui-error {
        line-height: 12px;
        margin: -12px 0 10px 0;
        font-size: 12px !important;
}
.qadpt-drawerContent .qadpt-drawerFooter {
    top: 10px;
    position: relative;
    padding: 0 16px;
}
.qadpt-drawerContent .qadpt-crtfrm-scratch {
    height: 100px;
    background-color: #eae2e2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    margin-top: 10px;
    gap: 10px;
}
.qadpt-btn {
    background-color: var(--primarycolor) !important;
    color: var(--white-color) !important;
    border-radius: 12px !important;
    width: 100% !important;
    text-transform: none !important;
    font-size: 16px !important;
    border: 0 !important;
    line-height: var(--button-lineheight) !important;
    padding: 10px 12px !important;
}
.qadpt-btn.disabled {
opacity:0.5;
}
.qadpt-ext-banner {
    display: flex;
    /* justify-content: space-between; */
    align-items: center;
    /* padding: 0 20px; */
    background-color: var(--ext-background);
    border-bottom: 1px solid var(--primarycolor);
    height: 55px;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999999;
}

.qadpt-ext-banner .qadpt-banner-button {
    border: 1px solid var(--primarycolor);
    padding: 8px;
    border-radius: 8px;
    color: var(--primarycolor);
    gap: 5px;
    max-height: 40px;
    height: 40px;
    text-transform: capitalize !important;
    background: rgba(255,255,255,0.6);
}
.qadpt-ext-banner input{
    height: inherit !important;
    padding: inherit !important;
    border: initial !important;
}
.qadpt-ext-banner .qadpt-banner-button.qadpt-icon {
    min-height: 40px;
    max-height: 40px;
    place-content: center;
    align-items: center;
}
/* .qadpt-ext-banner .qadpt-banner-button .qadp-btn-icon{
    margin-top: 5px;
} */
.qadpt-ext-banner .qadpt-left-banner,
.qadpt-ext-banner .qadpt-center-banner,
.qadpt-ext-banner .qadpt-right-banner {
    display: flex;
    align-items: center;
    gap: 6px;
    max-width: 33.33%;
    width: 33.33%;
}
.qadpt-ext-banner .qadpt-left-banner{
    padding-left: 10px;
}

.qadpt-ext-banner .qadpt-center-banner{
    justify-content: center !important;
}
.qadpt-ext-banner .qadpt-right-banner {
    justify-content: right !important;
    padding-right: 10px;
}
.qadpt-ext-banner .qadpt-left-banner button, .qadpt-ext-banner .qadpt-center-banner button, .qadpt-ext-banner .qadpt-right-banner button {
    display: flex;
    align-items: center;
}
.qadpt-ext-banner .qadpt-left-banner button span, .qadpt-ext-banner .qadpt-center-banner button span, .qadpt-ext-banner .qadpt-right-banner button span{
    max-width: 150px !important;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: flex;
    align-items: center;
}
.qadpt-ext-banner .qadpt-right-banner .qadpt-save{
opacity: 0.5;
}
.qadpt-threedot-popup {
    z-index: 999999 !important;
}
.qadpt-threedot-popup .MuiPopover-paper {
    max-width: 150px;
    max-height: 200px;
    padding: 15px;
    margin-top: 40px;
    margin-left: 100px;
}
.qadpt-popup-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
}
.qadpt-popup-item.disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: default;
}
.qadpt-popup-item:last-child {
    margin-bottom: 0;
}
.qadpt-popup-icon {
    margin-right: 10px;
    display: flex;
}


.qadpt-popup-text {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    text-align: left;
}

/* .MuiPopover-root .MuiSelect-root{
    z-index: 1000000 !important;
} */
.qadpt-acnt-drpdwn{
    z-index: 9999 !important;
}
.qadpt-acnt-drpdwn .MuiPaper-root.MuiPaper-elevation{
    top: 110px !important;
    left: 220px !important;
    width: calc(100vh - 370px);
    max-height: calc(100% - 130px);
}
.qadpt-acnt-drpdwn .MuiPaper-root.MuiPaper-elevation li{
    white-space: normal;
    word-break: break-word;
}

.qadpt-designpopup-v2 {
    width: 220px;
    border-radius: 8px;
    padding: 16px;
    background-color: rgba(246, 238, 238, 1);
    display: flex;
    flex-direction: column;
    transition: height 0.3s ease;

}

.qadpt-designpopup {
    width: 250px;
    border-radius: 8px;
    /* padding: 16px; */
    background-color: rgba(246, 238, 238, 1);
    position: fixed;
    z-index: 111111;
        display: flex;
    flex-direction: column;
    transition: height 0.3s ease;
    top: 60px;
    left: 10px;
}
.qadpt-designpopup.qadpt-btnsettings {
    /* left: auto !important; */
}

.qadpt-designpopup.qadpt-imgset{
	background-color: #fff !important;
}
.qadpt-designpopup .qadpt-content .qadpt-design-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}
.qadpt-designpopup .qadpt-content .qadpt-design-header .qadpt-title {
    font-weight: 600;
    color: var(--primarycolor);
    font-size: 16px !important;
    text-align: center;
}
.qadpt-designpopup .qadpt-content .qadpt-design-header svg {
    font-size: 16px;
}
.qadpt-designpopup .qadpt-content .qadpt-design-btn {
    width: 100%;
    justify-content: flex-start;
    background-color: #eae2e2;
    color: #444444;
    text-transform: none;
    margin-bottom: 5px;
    border-radius: 12px;
    padding: 8px 12px;
    align-items: center;
    font-weight: 600;
}
.qadpt-designpopup .qadpt-content .qadpt-design-btn .MuiButton-icon{
    background: rgba(95, 158, 160, 0.2);
    border-radius: 100px;
    padding: 4px 8px;
}

.qadpt-designpopup .qadpt-content .qadpt-design-btn .qadpt-hotsicon {
    margin-right: 8px;
    height: auto;
    background: rgba(95, 158, 160, 0.2);
    border-radius: 100px;
    margin-left: -5px;
    padding: 4px 8px;
    line-height: 12px;
}

.qadpt-designpopup .qadpt-content .qadpt-design-btn .qadpt-hotsicon svg{
    height: 23px;
    width: 23px;
}
.qadpt-designpopup .qadpt-content .qadpt-drawerFooter{
    padding: 15px;
    padding-top: 0;
}


.qadpt-designpopup .qadpt-content .qadpt-design-btn:hover {
    background-color: #d8d4d2;
}
.qadpt-designpopup .qadpt-content .qadpt-design-btn svg {
    color: var(--primarycolor);
    border-radius: 50px;
    height: 24px;
    width: 24px;
}
.qadpt-designpopup .qadpt-content .qadpt-status-container {
    display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 15px;
        padding-top: 0;
}


.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-label {
    text-align: left;
}
/* .qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group {
    margin-bottom: 16px;
} */
.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group button {
    text-transform: capitalize;
    height: 35px;
    width: 80px;
    font-size: 12px !important;
}
.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group .MuiToggleButton-root.Mui-selected {
    border: 1px solid var(--primarycolor);
}
.qadpt-designpopup .qadpt-content .qadpt-customfield {
    background-color: #eae2e2;
    border-radius: 8px;
    height: calc(100vh - 210px);
}
.qadpt-designpopup .qadpt-content .qadpt-customfield textarea {
    height: calc(100vh - 210px) !important;
}
.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root {
    height: 100%;
    width: 110%;
}
.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root fieldset {
    border: none;
}
.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root:hover fieldset {
    border-color: #495e58;
}
.qadpt-designpopup .qadpt-position-grid {
    padding: 8px;
    background-color: var(--back-light-color);
    border-radius: var(--button-border-radius);
    margin-bottom: 5px;
}
.qadpt-designpopup .qadpt-position-grid .MuiGrid-root {
    background: var(--ext-background);
    width: 100%;
    margin: 0;
    border-radius: 10px;
}
.qadpt-designpopup .qadpt-position-grid .qadpt-ctrl-title {
    text-align: left;
    font-size: 14px !important;
    font-weight: 600;
}
.qadpt-designpopup .qadpt-controls {
    padding: 15px;
    padding-top: 0;
}

.qadpt-designpopup .qadpt-controls .qadpt-control-box {
    display: flex;
    align-items: center;
    background-color: var(--back-light-color);
    border-radius: var(--button-border-radius);
    height: 40px;
    padding-right: 8px;
    margin-bottom: 5px;
    justify-content: space-between;
}
.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-label {
    font-size: 14px;
    margin-right: auto;
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #444444;
    text-align: left;
    padding: 8px;
}

.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input {
    width: 77px;
    margin-left: auto;
}
.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input input {
    text-align: right;
    padding-right: 5px !important;
    padding-left: 5px !important;
}

.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input .MuiOutlinedInput-root,
.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input.MuiOutlinedInput-root {
    border-radius: 12px;
    height: 30px;
    font-size: 14px;
    background-color: #f1ecec;
}
.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input .MuiSelect-select {
    height: 20px;
    font-size: 14px;
    /* background-color: var(--white-color); */
    border-radius: 12px;
}

.qadpt-designpopup .qadpt-controls .qadpt-color-input {
    width: 20px;
    height: 20px;
    padding: 0 !important;
    border-radius: 50%;
    border: 1px solid var(--border-color);
}
.qadpt-designpopup .qadpt-controls .qadpt-color-input::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 50%;
}
.qadpt-designpopup .qadpt-controls .qadpt-color-input::-webkit-color-swatch {
    border-radius: 50%;
    border: none;
}
.qadpt-designpopup .qadpt-content .qadpt-canblock{
    max-height: calc(100vh - 184px);
    overflow: auto;
}
.qadpt-designpopup .qadpt-content .qadpt-canblock.qadpt-btnpro{
    max-height: calc(100vh - 245px) !important;
}

/* p.styles */

.htmlbanner .MuiBox-root{
height: 40px;
}
/* end */
/* .MuiInputBase-input:-webkit-autofill{
	background-color: white !important;
	box-shadow: 0 0 0px 1000px white inset !important;
	color: #000 !important;
} */
.custom-popover-root{
    z-index: 9999 !important;
}
.qadpt-toaster {
    top: 30px !important;
    width: 40%;
    z-index: 9999999 !important;
    overflow: hidden !important;
}
.qadpt-toaster .MuiAlert-message {
    display: block;
    overflow: hidden !important;
    word-break: break-word;
    width: 100%;
    text-align: left;
}
.qadpt-toaster.qadpt-toaster-success {
    border: 1px solid #2e7d32;
}
.qadpt-toaster.qadpt-toaster-error {
    border: 1px solid #f00;
}
.qadpt-toaster .qadpt-alert {
    width: 150%;
    display: flex;
    align-items: flex-start;

}
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    }
}

.pulse-animation {
    animation: pulse 1.5s infinite;
}
.qadpt-guide-popup{
    z-index: 99999 !important;
}
.qadpt-previewdata{
    position: inherit !important;
}

.qadpt-tooltip-header {
    display: block;
    font-weight: 600;
    color: var(--primarycolor);
  }

  .qadpt-ext-banner .qadpt-tooltip-subtext {
    display: block;
    font-size: 14px !important;
    color: var(--primarycolor);
  }

  .qadpt-tour-popup{
    z-index: 9999999 !important;
}
.MuiButtonBase-root.MuiMenuItem-root {
    margin: 0 10px;
    padding: 10px;
	&.Mui-selected,&:hover {
	  border-radius: 12px;
	  margin: 0 10px;
	  padding: 10px;
	}
}

.MuiOutlinedInput-root.Mui-error fieldset{
    border-color: #e6a957 !important;
}
/* Container for the switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    height: 20px;
    width: 36px;
  }

  /* Hide the default checkbox input */
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  /* The slider background */
  .toggle-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
    margin: 0 !important;
  }

  /* The circle/knob */
  .toggle-switch .slider:before {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    left: 0px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  /* Checked state styles */
  .toggle-switch input:checked + .slider {
    background-color: var(--primarycolor);  /* Teal/turquoise color */
  }

  .toggle-switch input:checked + .slider:before {
    transform: translateX(20px);
  }

  /* Focus styles for accessibility */
  .toggle-switch input:focus + .slider {
    box-shadow: 0 0 1px var(--primarycolor);
  }

  /* Optional: Disabled state styles */
  .toggle-switch input:disabled + .slider {
    opacity: 0.5;
    cursor: not-allowed;
  }
  .step-input input{
    padding:6px 10px !important;
    border: 1px solid #a8a8a8 !important;
    width: -webkit-fill-available;
    border-radius: 6px;
    background: rgb(255, 255, 255);
    outline: none;
    text-align: left;
    margin-bottom: 5px;
  }
  .step-input.qadpt-stbdr input{
    border: 1px solid #e9a971 !important;
  }

  .qadpt-overlay.notcollapsed{
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99999;
  }

  .qadpt-chkstp{
    position: relative;
  }
  .qadpt-chkstp::before{
    background: var(--chkcolor);
    content: '';
    position: absolute;
    display: block;
    width : 6px;
    height: 100%;
   border-top-right-radius: 10px;
   border-bottom-right-radius: 10px;
  }


  .qadpt-launcher-config {
    padding: 16px;
    font-family: sans-serif;
  }

  .qadpt-type-selector {
    display: flex;
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid #e0e0e0;
    width: fit-content;
  }

  .qadpt-type-option {
    padding: 8px 10px;
    border-radius: 16px;
    background: #E5DADA;
    cursor: pointer;
    border: none !important;
  }

  .qadpt-type-option.selected {
    border: 1px solid var(--primarycolor) !important;
  }

  .qadpt-text-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }

  .qadpt-color-input {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
  }

.qadpt-chkpopdesc{
    font-size: 14px;
    line-height: 1.5;
    margin: 0px;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
}

/* Create With AI Button Styles start */
.qadpt-ai-container {
    border-radius: 10px;
    padding: 17px;
    margin: 10px 0;
    color: white;
    text-align: left;
    background: linear-gradient(360deg, #04417F 0%, #0776E5 100%);
}

.beta {
    width: 47px;
    background: #000;
    font-weight: bold;
    text-align: center;
    line-height: 18px;
    font-size: 10px !important;
    position: absolute;
    top: 19px;
    right: 23px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    z-index: 10;
    border-radius:6px 0px 0px 6px;
    overflow: hidden;
    height: 18px;
    color: #fff;
    animation: betaPulse 0.8s ease-in-out infinite alternate;
    letter-spacing: 0.5px;
}
  @keyframes betaPulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 4px rgba(77, 74, 70, 0.5);
      
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 4px rgba(77, 74, 70, 0.8);

     
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 4px rgba(77, 74, 70, 0.5);

     
    }
  }
  

.qadpt-ai-title {
    width: calc(100% - 40px);
    margin-bottom: 10px;
    line-height: 1.3;
}

.qadpt-button {
    display: flex;
    align-items: center;
    place-content: center;
    background-color: white;
    border-radius: 12px;
    padding: 8px 12px;
    margin-top: 10px;
    cursor: pointer;
    border: none;
    width: 100%;
    transition: background-color 0.3s ease;
}

.qadpt-button:hover {
    background-color: #f0f0f0;
}

.qadpt-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.qadpt-icon .back-icon svg path {
    fill: var(--primarycolor);
}
.leftDrawer.collapsed .qadpt-drawerHeader .side-menu .qadpt-ai-button.active{

}

.qadpt-text {
    font-size: 16px;
    color: #04417F;
}
/* Create With AI Button Styles ends*/
.qadpt-desc {
    color: #8D8D8D;
    line-height: 1.5;
    font-size: 14px;
    height:4.3rem;
    margin-bottom: 10px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-word;

  }
  .MuiLinearProgress-root{
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
  }

  .qadpt-editor{
    border-radius: 12px;
border: 0.6px solid #717171;
background: rgba(9, 17, 17, 0.32);
backdrop-filter: blur(5.349999904632568px);
padding: 10px 12px;
width: calc(50% - 550px);
    margin: 20px;
    z-index: 9999999;
    position: fixed;
    top: 0;
    display: flex;
        align-items: center;
        place-content: center;
        left: 0;
        cursor: pointer;
}
.qadpt-editor.qadpt-baneditor{
    width: calc(50% - 600px);

}
.qadpt-editor button{
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0;
    width: auto;

}

.qadpt-editor button svg{
    display: flex;
}
.qadpt-editor button .qadpt-bansep svg,
.qadpt-editor button .qadpt-bansep path,
.qadpt-editor button .qadpt-sep svg,
.qadpt-editor button .qadpt-sep path {
    fill: #fff;
    stroke: #fff;
}


.qadpt-editor button .qadpt-sep{
    border-right: 1px solid #ccc;
    display: flex ;
    flex-direction: row;
    gap: 12px;
    align-items: center;
}
.qadpt-editor button .qadpt-bansep{
    display: flex ;
    flex-direction: row;
    gap: 12px;
    align-items: center;
}
.qadpt-editor button .edt-txt{
    margin-right: 10px;
    color: #fff;
    text-transform: capitalize;
}
.qadpt-editor.qadpt-baneditor button .edt-txt{
    margin-right: 0px !important;
}
.qadpt-editor .qadpt-curstep{
    color: #fff;
    text-transform: capitalize;
    margin-left: 11px;
    font-weight: 500;
    cursor: default;
}

.MuiTextField-root fieldset{
    background-color: transparent !important;
}
.MuiTextField-root:not(.qadpt-webclonepopup .MuiTextField-root) legend {
    display: none;
}
.MuiInputBase-root fieldset{
    background-color: transparent !important;
}
.MuiInputBase-root:not(.qadpt-selectaccount):not(.qadpt-webclonepopup .MuiInputBase-root) legend {
    display: none;
}

button:focus,
button:hover {
  /* border-color: inherit !important; */
  box-shadow: none !important;
}
.MuiTextField-root input:focus {
    box-shadow: none !important;
    color: inherit !important;
    outline: none !important;
    border :none !important;
  }

  input:-webkit-autofill {
    box-shadow: 0 0 0 1000px white inset !important;
    -webkit-text-fill-color: inherit !important;
  }
  /* .qadpt-imgsec-popover{ 
    z-index: 99999 !important;
  }
  .qadpt-bunprop{
    z-index: 99999 !important;
  }
  .qadpt-imgset{
    z-index: 99999 !important; 
  }
  .qadpt-secprop{
    z-index: 99999 !important;
  } */

.MuiPopover-root:not(.qadpt-turstp):not(.qadpt-index){
    z-index: 999999 !important;
}
.MuiPopper-root:not(.qadpt-tlprte){
    z-index:999999 !important;
}

.qadpt-image-upload svg {
    border: 1px solid #5F9EA0;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    background: #fff;
}

.qadpt-image-upload svg rect {
    stroke: none;
}

.MuiDialog-root{
    z-index:99999 !important; 
}

.jodit-react-container br{
    display: inline !important;
}
.body.dynamic-body-style header {
    position: relative !important;
}
.ps__rail-x {
    display: none !important;
  }
