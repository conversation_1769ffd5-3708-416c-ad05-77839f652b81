import React, { useState, useEffect, useRef } from "react";
import { <PERSON>over, Button, Typography, Box, LinearProgress, DialogActions,IconButton, MobileStepper } from "@mui/material";
import { CustomIconButton } from "./Button";
import CloseIcon from "@mui/icons-material/Close";
import { PopoverOrigin } from "@mui/material";
import useDrawerStore, { DrawerState } from "../../store/drawerStore";
import { GuideData } from "../drawer/Drawer";
import BannerEndUser from "../Bannerspreview/Banner";
import BannerStepPreview from "../tours/BannerStepPreview"
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';


// Helper function to get an element by XPath
const getElementByXPath = (xpath: string): HTMLElement | null => {
    try {
        const result = document.evaluate(
            xpath,
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
        );
        return result.singleNodeValue as HTMLElement;
    } catch (error) {
        console.error("Error evaluating XPath:", error);
        return null;
    }
};

// Helper function to convert hex color to rgba with opacity
const hexToRgba = (hex: string, opacity: number): string => {
	// Remove # if present
	hex = hex.replace('#', '');

	// Parse hex values
	const r = parseInt(hex.substring(0, 2), 16);
	const g = parseInt(hex.substring(2, 4), 16);
	const b = parseInt(hex.substring(4, 6), 16);

	return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
interface PopupProps {
    handlecloseBannerPopup: any;
    guideStep: any[];
    anchorEl: null | HTMLElement;
    onClose: () => void;
    onPrevious: () => void;
    onContinue: () => void;
    title: string;
    text: string;
    imageUrl?: string;
    videoUrl?: string;
    previousButtonLabel: string;
    continueButtonLabel: string;
    previousButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    continueButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    currentStep: number;
    totalSteps: number;
    onDontShowAgain: () => void;
    progress: number;
    textFieldProperties?: any;
    imageProperties?: any;
    customButton?: any;
    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };
    canvasProperties?: {
        Position?: string;
        Padding?: string;
        Radius?: string;
        BorderSize?: string;
        BorderColor?: string;
        BackgroundColor?: string;
        Width?: string;
    };
    htmlSnippet: string;
    OverlayValue: boolean;
    backgroundC: any;
    Bposition: any;
    bpadding: any;
    Bbordercolor: any;
    BborderSize: any;
    savedGuideData: GuideData | null;
    selectedTemplate:any
    ProgressColor:any}

const AnnouncementPopup: React.FC<PopupProps> = ({
    selectedTemplate,
    handlecloseBannerPopup,
    backgroundC,
    Bposition,
    bpadding,
    Bbordercolor,
    BborderSize,
    guideStep,
    anchorEl,
    onClose,
    onPrevious,
    onContinue,
    title,
    text,
    imageUrl,
    videoUrl,
    previousButtonLabel,
    continueButtonLabel,
    currentStep,
    totalSteps,
    onDontShowAgain,
    progress,
    textFieldProperties,
    imageProperties,
    customButton,
    modalProperties,
    canvasProperties,
    htmlSnippet,
    previousButtonStyles,
    continueButtonStyles,
    OverlayValue,
    savedGuideData
}) => {

    const {
        setCurrentStep,
        selectedOption,
        steps,
        setBannerPreview,
        bannerPreview,
        announcementPreview, setAnnouncementPreview,
        ProgressColor,
		setProgressColor
	} = useDrawerStore((state: DrawerState) => state);
    const [Overlayvalue, setOverlayValue] = useState(false);
    const handleContinue = () => {
        if (selectedTemplate !== "Tour") {
            if (currentStep < totalSteps) {
                setCurrentStep(currentStep + 1);
                onContinue();
            }
        }
        else {
            if (currentStep !== savedGuideData?.GuideStep?.length) {
                setCurrentStep(currentStep + 1);
            }
        }
    };

    const handlePrevious = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
            onPrevious();
        }
    };
    // Initialize Overlayvalue state from props only once
    useEffect(() => {
        setOverlayValue(!!OverlayValue);
    }, []);
    const imageFit = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Fit || 'contain';
    const getAnchorAndTransformOrigins = (position: string): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {
        switch (position) {
            case "top-left":
                return { anchorOrigin: { vertical: "top", horizontal: "left" }, transformOrigin: { vertical: "bottom", horizontal: "right" } };
            case "top-right":
                return { anchorOrigin: { vertical: "top", horizontal: "right" }, transformOrigin: { vertical: "bottom", horizontal: "left" } };
            case "bottom-left":
                return { anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "right" } };
            case "bottom-right":
                return { anchorOrigin: { vertical: "bottom", horizontal: "right" }, transformOrigin: { vertical: "center", horizontal: "left" } };
            case "center-center":
                return { anchorOrigin: { vertical: "center", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
            case "top-center":
                return { anchorOrigin: { vertical: "top", horizontal: "center" }, transformOrigin: { vertical: "bottom", horizontal: "center" } };
            case "left-center":
                return { anchorOrigin: { vertical: "center", horizontal: "left" }, transformOrigin: { vertical: "center", horizontal: "right" } };
            case "bottom-center":
                return { anchorOrigin: { vertical: "bottom", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
            case "right-center":
                return { anchorOrigin: { vertical: "center", horizontal: "right" }, transformOrigin: { vertical: "center", horizontal: "left" } };
            default:
                return { anchorOrigin: { vertical: "center", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
        }
    };
    const getPopoverPositionStyle = (position: string = "center-center") => {
        // Constants
        const EDGE_PADDING = 12; // Padding from screen edges (in px)

        // Basic reset for all positioning properties
        const baseStyle = {
            position: 'fixed',
            top: 'auto !important',
            right: 'auto',
            bottom: 'auto',
            left: 'auto !important',
            transform: 'none'
        };

         // Apply specific positioning based on selected position
         switch (position) {
            case "top-left":
                return {
                    ...baseStyle,
                    top: `${EDGE_PADDING + 10 }px !important`,
                    left: `${EDGE_PADDING}px !important`
                };
            case "top-center":
                return {
                    ...baseStyle,
                    top: `${EDGE_PADDING + 10}px !important`,
                    left: '50% !important',
                    transform: 'translateX(-50%)'
                };
            case "top-right":
                return {
                    ...baseStyle,
                    top: `${EDGE_PADDING + 10 }px !important`,
                    right: `${EDGE_PADDING + 5}px`
                };
           // case "left-center":
            case "left-center":
                return {
                    ...baseStyle,
                    top: '54% !important',
                    left: `${EDGE_PADDING}px !important`,
                    transform: 'translateY(-50%)'
                };
            //case "center-center":
            case "center-center":
                return {
                    ...baseStyle,
                    top: '54% !important',
                    left: '50% !important',
                    transform: 'translate(-50%, -50%)'
                };
           // case "right-center":
            case "right-center":
                return {
                    ...baseStyle,
                    top: '54% !important',
                    right: `${EDGE_PADDING + 5}px`,
                    transform: 'translateY(-50%)'
                };
            case "bottom-left":
                return {
                    ...baseStyle,
                    bottom: `${EDGE_PADDING }px !important`,
                    left: `${EDGE_PADDING}px !important`
                };
            case "bottom-center":
                return {
                    ...baseStyle,
                    bottom: `${EDGE_PADDING}px`,
                    left: '50% !important',
                    transform: 'translateX(-50%)'
                };
            case "bottom-right":
                return {
                    ...baseStyle,
                    bottom: `${EDGE_PADDING }px`,
                    right: `${EDGE_PADDING + 5}px`
                };
            default:
                return {
                    ...baseStyle,
                    top: '50% !important',
                    left: '50% !important',
                    transform: 'translate(-50%, -50%)'
                };
        }
    };

    const interactWithPage = savedGuideData?.GuideStep?.[currentStep - 1]?.Tooltip?.InteractWithPage;
    const { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || "center center");

    const textStyle = {
        fontWeight: textFieldProperties?.TextProperties?.Bold ? "bold" : "normal",
        fontStyle: textFieldProperties?.TextProperties?.Italic ? "italic" : "normal",
        color: textFieldProperties?.TextProperties?.TextColor || "#000000",
        textAlign: textFieldProperties?.Alignment || "left",
    };


    const imageStyle = {
        maxHeight: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.MaxImageHeight || "500px",
        textAlign: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Alignment || "center",
        objectFit: imageFit || "contain",
        width: "100%",
        height: `${imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || 250}px`,
        background: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.BackgroundColor || "#ffffff",
    };

    const renderHtmlSnippet = (snippet: string) => {
        // Return the raw HTML snippet for rendering
        return { __html: snippet.replace(/(<a\s+[^>]*href=")([^"]*)("[^>]*>)/g, (match, p1, p2, p3) => {
            return `${p1}${p2}" target="_blank"${p3}`;
        }) };
    };

    // Safely group buttons, handling potential null/undefined values
    const groupedButtons = React.useMemo(() => {
        if (!customButton || !Array.isArray(customButton) || customButton.length === 0) {
            return {};
        }

        return customButton.reduce((acc: any, button: any) => {
            if (!button) return acc;

        const containerId = button.ContainerId || "default"; // Use a ContainerId or fallback
        if (!acc[containerId]) {
          acc[containerId] = [];
        }
        acc[containerId].push(button);
        return acc;
      }, {});
    }, [customButton]);

	const canvasStyle = {
		position: canvasProperties?.Position || "center-center",
		borderRadius: `${canvasProperties?.Radius ? canvasProperties.Radius : 8}px !important`,
		borderWidth: canvasProperties?.BorderSize || "0px",
		borderColor: canvasProperties?.BorderColor || "transparent",
		borderStyle: "solid",
		backgroundColor: canvasProperties?.BackgroundColor || "white",
		width: canvasProperties?.Width ? `${canvasProperties?.Width}px` : "500px",
	};
	const dissmissIconColor = "red";
	const ActionButtonBackgroundcolor = "#f0f0f0";
	const overlay: boolean = Overlayvalue;
	const sectionHeight = imageProperties[0]?.CustomImage?.[0]?.SectionHeight || "auto";
	const openInNewTab = true;
	// Determine progress bar state based on guide type and current step
	const enableProgress = (() => {
		// For AI-created announcements, check the current step's data
		const currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];
		const firstStepData = savedGuideData?.GuideStep?.[0];

		if (currentStepData?.Tooltip?.EnableProgress !== undefined) {
			return currentStepData.Tooltip.EnableProgress;
		}
		// Fallback to first step for backward compatibility
		return firstStepData?.Tooltip?.EnableProgress || false;
	})();	function getProgressTemplate(selectedOption: any) {
		if (selectedOption === 1) {
			return "dots";
		} else if (selectedOption === 2) {
			return "linear";
		} else if (selectedOption === 3) {
			return "BreadCrumbs";
		}
        else if (selectedOption === 4) {
			return "breadcrumbs";
		}

		return savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || "dots";
	}
	const progressTemplate = getProgressTemplate(selectedOption);
	const renderProgress = () => {
        if (!enableProgress) return null;

		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={steps.length}
					position="static"
					activeStep={currentStep - 1}
					sx={{ backgroundColor: "transparent",  "& .MuiMobileStepper-dotActive": {
                        backgroundColor: ProgressColor, // Active dot
                      }, }}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}
        if (progressTemplate === "BreadCrumbs") {
			return (
                <Box sx={{display: "flex",
					alignItems: "center",
					placeContent: "center",
					gap: "4px",padding:"8px"}}>
                  {/* Custom Step Indicators */}

                    {Array.from({ length: steps.length }).map((_, index) => (
                      <div
                        key={index}
                        style={{
                          width: '14px',
                          height: '4px',
                          backgroundColor: index === currentStep - 1 ? ProgressColor : hexToRgba(ProgressColor, 0.45), // Active color and inactive color
                          borderRadius: '100px',
                        }}
                      />
                    ))}

                </Box>
              );
		}
		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{display: "flex",
				alignItems: "center",
				placeContent: "flex-start",padding:"8px"
				}}>
					<Typography sx={{color: ProgressColor}}>
                    Step {currentStep} of {steps.length}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box >
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
                            sx={{
                                height: "6px",
								borderRadius: "20px",
								margin: "6px 10px",
								backgroundColor: hexToRgba(ProgressColor, 0.45),
                                '& .MuiLinearProgress-bar': {
                                backgroundColor: ProgressColor, // progress bar color
                              },}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
    };
    // State to track if scrolling is needed
    const [needsScrolling, setNeedsScrolling] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);
  const scrollbarRef = useRef<any>(null);
    const handleButtonAction = (action: any) => {
		if (action.Action === "open-url" || action.Action === "open" || action.Action==="openurl") {
			const targetUrl = action.TargetUrl;
			if (action.ActionValue === "same-tab") {
				// Open the URL in the same tab
				window.location.href = targetUrl;
			} else {
				// Open the URL in a new tab
				window.open(targetUrl, "_blank", "noopener noreferrer");
			}
		} else {
			if (action.Action == "Previous" || action.Action == "previous" || action.ActionValue == "Previous" || action.ActionValue == "previous") {
				handlePrevious();
			} else if (action.Action == "Next" || action.Action == "next" || action.ActionValue == "Next" || action.ActionValue == "next") {
				handleContinue();
			} else if (action.Action == "Restart" || action.ActionValue == "Restart") {
				// Reset to the first step
				setCurrentStep(1);
				// If there's a specific URL for the first step, navigate to it
				if (savedGuideData?.GuideStep?.[0]?.ElementPath) {
					const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);
					if (firstStepElement) {
						firstStepElement.scrollIntoView({ behavior: 'smooth' });
					}
				}
			}
		}
	};
	function getAlignment(alignment: string) {
		switch (alignment) {
			case "start":
				return "flex-start";
			case "end":
				return "flex-end";
			case "center":
			default:
				return "center";
		}
	}

    const position = canvasProperties?.Position || "center-center";
    const positionStyle = getPopoverPositionStyle(position);
    // Check if content needs scrolling with improved detection
	useEffect(() => {
		const checkScrollNeeded = () => {
			if (contentRef.current) {
				// Force a reflow to get accurate measurements
				contentRef.current.style.height = 'auto';
				const contentHeight = contentRef.current.scrollHeight;
				const containerHeight = 320; // max-height value
				const shouldScroll = contentHeight > containerHeight;


				setNeedsScrolling(shouldScroll);

				// Force update scrollbar
				if (scrollbarRef.current) {
					// Try multiple methods to update the scrollbar
					if (scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
					// Force re-initialization if needed
					setTimeout(() => {
						if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
							scrollbarRef.current.updateScroll();
						}
					}, 10);
				}
			}
		};

		
		checkScrollNeeded();

		
		const timeouts = [
			setTimeout(checkScrollNeeded, 50),
			setTimeout(checkScrollNeeded, 100),
			setTimeout(checkScrollNeeded, 200),
			setTimeout(checkScrollNeeded, 500)
		];

		
		let resizeObserver: ResizeObserver | null = null;
		let mutationObserver: MutationObserver | null = null;

		if (contentRef.current && window.ResizeObserver) {
			resizeObserver = new ResizeObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			resizeObserver.observe(contentRef.current);
		}

		
		if (contentRef.current && window.MutationObserver) {
			mutationObserver = new MutationObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			mutationObserver.observe(contentRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['style', 'class']
			});
		}

		return () => {
			timeouts.forEach(clearTimeout);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
			if (mutationObserver) {
				mutationObserver.disconnect();
			}
		};
	}, [currentStep]);
	return (
		<div>
			{OverlayValue && selectedTemplate!=="Tour"&&(
				<div
					style={{
						position: "fixed",
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						backgroundColor: "rgba(0, 0, 0, 0.5)",
						zIndex: 998,
						pointerEvents: selectedTemplate === "Tour" ? "auto" : "none",
					}}
				/>
			)}
			<Popover
				className="previewdata qadpt-index"
				open={Boolean(anchorEl)}
				anchorEl={anchorEl}
				onClose={undefined}
				anchorOrigin={anchorOrigin}
				transformOrigin={transformOrigin}
				sx={{
					position:selectedTemplate==="Tour"?"absolute !important": interactWithPage && OverlayValue === false ? "absolute !important" : "",
					zIndex:selectedTemplate==="Tour"?"auto !important": interactWithPage && OverlayValue === false ? "auto !important" : "",
					"pointer-events": anchorEl ? "auto" : "auto",
					"& .MuiPaper-root:not(.MuiMobileStepper-root)": {
						zIndex: "999 !important",
						// zIndex: 999,
						// borderRadius: "1px",
						...canvasStyle,
						...positionStyle,
                        margin: "0 !important",
						transform: `${positionStyle.transform} !important`,

						overflow: "visible",
					},
				}}
				disableScrollLock={true}
			>
				<div style={{ placeContent: "end", display: "flex" }}>
					{modalProperties?.DismissOption && (
						<IconButton
							sx={{
								position: "fixed",
								boxShadow: "rgba(0, 0, 0, 0.06) 0px 4px 8px",
								left: "auto",
								right: "auto",
								margin: "-15px",
								background: "#fff !important",
								border: "1px solid #ccc",
								zIndex: "999999",
								borderRadius: "50px",
								padding: "5px !important",
							}}
						>
							<CloseIcon sx={{ zoom: "0.7", color: "#000" }} />
						</IconButton>
					)}
				</div>
                <PerfectScrollbar
				key={`scrollbar-${needsScrolling}`}
				ref={scrollbarRef}
				style={{ maxHeight: "550px" }}
				options={{
					suppressScrollY: !needsScrolling,
					suppressScrollX: true,
					wheelPropagation: false,
					swipeEasing: true,
					minScrollbarLength: 20,
					scrollingThreshold: 1000,
					scrollYMarginOffset: 0
				}}
			>
				
					<Box
						style={{
							padding: canvasProperties?.Padding ? `${canvasProperties?.Padding}px` : "4px",
							height: sectionHeight,
                        }}
                        ref={contentRef}
					>
						<Box
							display="flex"
							flexDirection="column"
							flexWrap="wrap"
							justifyContent="center"
						>
							{imageProperties?.map((imageProp: any, propIndex: number) =>
								imageProp.CustomImage.map((customImg: any, imgIndex: number) => (
									<Box
										key={`${imageProp.Id}-${imgIndex}`}
										component="img"
										src={customImg.Url}
										alt={customImg.AltText || "Image"}
										sx={{
											maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || "500px",
											textAlign: imageProp.Alignment || "center",
											objectFit: customImg.Fit || "contain",
											background: customImg.BackgroundColor || "#ffffff",
											width: "100%",
											height: `${customImg.SectionHeight || 250}px`,
											//height: "100%",
											margin: 0,
											padding: 0,
											borderRadius: "0",
										}}
										onClick={() => {
											if (imageProp.Hyperlink) {
												const targetUrl = imageProp.Hyperlink;
												window.open(targetUrl, "_blank", "noopener noreferrer");
											}
										}}
										style={{ cursor: imageProp.Hyperlink ? "pointer" : "default" }}
									/>
								))
							)}
						</Box>


						{textFieldProperties?.map(
							(textField: any, index: any) =>
								textField.Text && (
									<Typography
										key={textField.Id || index} // Use a unique key, either Id or index
										className="qadpt-preview"
										sx={{
											whiteSpace: "pre-wrap",
											wordBreak: "break-word",
                textAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,
											marginTop: 1,
											color: textField.TextProperties?.TextColor || textStyle.color,
											padding:"5px"
										}}
										dangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML
									/>
								)
						)}

						{Object.keys(groupedButtons).map((containerId) => (
							<Box
								key={containerId}
								sx={{
									display: "flex",
									justifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),
									flexWrap: "wrap",
									margin: 0,
									backgroundColor: groupedButtons[containerId][0]?.BackgroundColor,
           padding: "10px",
								}}
							>
								{groupedButtons[containerId].map((button: any, index: number) => (
									<Button
										key={index}
										onClick={() => handleButtonAction(button.ButtonAction)}
										variant="contained"
										sx={{
											marginRight: "13px",
											margin: "0 5px",
											backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#007bff",
											color: button.ButtonProperties?.ButtonTextColor || "#fff",
											border: `2px solid ${button.ButtonProperties?.ButtonBorderColor}` || "transparent",
											fontSize: button.ButtonProperties?.FontSize || "14px",
											width: button.ButtonProperties?.Width || "auto",
											// paddingTop: "3px",
											//                         paddingRight: "16px",
											//                         paddingBottom: "3x",
											//                         paddingLeft:"16px",
											textTransform: "none",
											borderRadius: button.ButtonProperties?.BorderRadius || "8px",
											padding: "var(--button-padding) !important",
                                            lineHeight: "var(--button-lineheight) !important",
                                            boxShadow: "none !important"
										}}
									>
										{button.ButtonName}
									</Button>
								))}
							</Box>
						))}
					</Box>
					
               </PerfectScrollbar>
                <div>
						{/* Render Step Progress */}
                        {totalSteps >= 1 && enableProgress ? <>{renderProgress()}</> : null}
                    </div>
			</Popover>
		</div>
	);
};

export default AnnouncementPopup;
