



import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@mui/material";
import { Button } from "@mui/material";
import useDrawerStore, { DrawerState } from '../../store/drawerStore';
import { ToursAnnouncementsIcon, ToursBannerIcon, ToursHotspotIcon, ToursTooltipIcon } from '../../assets/icons/icons';


const FeatureSelectionModal: React.FC<{ isOpen: boolean; onClose: () => void; guideName: any; setStepData: any; stepData:any,count:any}> = ({ isOpen, onClose,guideName,setStepData ,stepData,count}) => {
    const [hoveredItem, setHoveredItem] = useState<Number|null>();
    const [selectedStepType, setSelectedStepType] = useState<string | null>(null); // Track selected step type
    const {
        setSelectedTemplate,
        setBannerPopup,
      setSelectedTemplateTour,
      setSteps,
      steps,
      setTooltipCount,
      tooltipCount,
      HotspotGuideDetails,
      setElementSelected,
      TooltipGuideDetails,
      HotspotGuideDetailsNew,
      setSelectedStepTypeHotspot,
      selectedTemplate,
      selectedTemplateTour,
      createWithAI
  } = useDrawerStore((state: DrawerState) => state);
  const [selectedStepStyle, setSelectedStepStyle] = useState({});
    const features = [
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursAnnouncementsIcon }} />,
        title: "Announcement",
        description: "An announcement of any new feature",
        action: () => {
          setSelectedStepType("Announcement");
          setSelectedStepStyle({
            borderColor: "#5F9EA0",
            background: "#F6FFFF",
          });
        },
      },
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursHotspotIcon }} />,
        title: "Hotspot",
        description: "Offer users quick tips",
        action: () => {
          setSelectedStepType("Hotspot");
        },
      },
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursBannerIcon }} />,
        title: "Banner",
        description: "Create in-line banners that get noticed",
        action: () => {
          setSelectedStepType("Banner");
        },
      },
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursTooltipIcon }} />,
        title: "Tooltip",
        description: "Anchored to selected elements",
        action: () => {
          setSelectedStepType("Tooltip");
        },
      },
    ];

    if (!isOpen) return null;
  const handleNextClick = () => {

    if ( (selectedTemplate==="Tour" &&(selectedStepType==="Banner"))) {
			let styleTag = document.getElementById("dynamic-body-style") as HTMLStyleElement;
			const bodyElement = document.body;

			// Add a dynamic class to the body
			bodyElement.classList.add("dynamic-body-style");

			if (!styleTag) {
				styleTag = document.createElement("style");
				styleTag.id = "dynamic-body-style";

				// Add styles for body and nested elements
				let styles = `
					.dynamic-body-style {
						padding-top: 50px !important;
						max-height:calc(100% - 55px);
					}

				`;

				styleTag.innerHTML = styles;
				document.head.appendChild(styleTag);
			}
		}


      if (selectedStepType) {
        // Based on selectedStepType, navigate and update steps
        if (selectedStepType === "Announcement") {
          TooltipGuideDetails();
          setSelectedTemplateTour("Announcement");
          setSelectedTemplate("Tour");
          setStepData({ ...stepData, type: "Announcement" });
        } else if (selectedStepType === "Hotspot") {
          HotspotGuideDetails();
          setSelectedTemplateTour("Hotspot");
          setSelectedTemplate("Tour");
          setSelectedStepTypeHotspot(true);
          setStepData({ ...stepData, type: "Hotspot" });
          setTooltipCount(tooltipCount + 1);
          setElementSelected(false);
        } else if (selectedStepType === "Banner") {
          TooltipGuideDetails();
          setSelectedTemplate("Tour");
          setSelectedTemplateTour("Banner");
          setStepData({ ...stepData, type: "Banner" });
          // Reset all banner canvas settings to defaults for new banner steps
          useDrawerStore.getState().resetBannerCanvasToDefaults();
          setBannerPopup(true);
        } else if (selectedStepType === "Tooltip") {
          TooltipGuideDetails();
          setSelectedTemplateTour("Tooltip");
          setSelectedTemplate("Tour");
          setStepData({ ...stepData, type: "Tooltip" });
          setTooltipCount(tooltipCount + 1);
        }

        const updatedSteps = steps.map(step => ({
          ...step,
          stepType: selectedStepType,
        }));

        setSteps(updatedSteps);
        onClose(); // Close the modal after proceeding
      }
    };
    if (createWithAI) {
      onClose();
      return null;
  }
    const isSelected = (title: string) => selectedStepType === title;
    const isHovered = (index: number) => hoveredItem === index;
    return (

      <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 999,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
      }}
    >
      <div
        className="qadpt-tours-container"
        style={{
          width: "100%",
          maxWidth: "663px",
          backgroundColor: "white",
          boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
          border: "1px solid #e0e0e0",
          height: "365px",
          borderRadius: "12px",
        }}
      >
        {/* Header Section */}
        <div
          className="qadpt-tour-header"
          style={{
            textAlign: "left",
            padding: "10px 15px",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
            }}
          >
            <span
              className="qadpt-title"
              style={{
                fontSize: "20px",
                fontWeight: 600,
              }}
            >
              {guideName}
            </span>
            <span
              style={{
                backgroundColor: "#C8E7E8",
                borderRadius: "4px",
                fontSize: "10px",
                width: "45px",
                height: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginLeft: "8px",
              }}
            >
              Step-1
            </span>
          </div>
          <div
            style={{
              fontSize: "13px",
              color: "#B1B1B1",
              lineHeight: "19.5px",
              letterSpacing: "0.3px",
            }}
          >
            Choose Step-1: Tour Type
          </div>
        </div>

        {/* Step Selection Section */}
        <div
          className="qadpt-tours-content"
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "center",
            gap: "10px",
            padding: "10px 15px",
          }}
        >
          {features.map((feature, index) => {
            const isSelected = selectedStepType === feature.title;
            const isHovered = hoveredItem === index;

            return (
              <div
                key={index}
                onClick={() => setSelectedStepType(feature.title)}
                onMouseEnter={() => setHoveredItem(index)}
                onMouseLeave={() => setHoveredItem(null)}
                style={{
                  width: "145px",
                  height: "218px",
                  textAlign: "center",
                  cursor: "pointer",
                  borderRadius: "7px",
                  padding: "12px 10px",
                  border: "0.5px solid",
                  borderColor: isSelected || isHovered ? "#5F9EA0" : "#E0E0E0",
                  background: isSelected || isHovered ? "#F6FFFF" : "#fff",
                  transition: "all 0.2s ease-in-out",
                }}
              >
                <div
                  style={{
                    fontSize: "24px",
                    fontWeight: "600px",
                    marginBlock: "5px",
                    height: "90px",
                  }}
                >
                  {feature.icon}
                </div>
                <div
                  style={{
                    fontSize: "16px",
                    fontWeight: "600",
                    marginBottom: "10px",
                  }}
                >
                  {feature.title}
                </div>
                <div
                  style={{
                    fontSize: "14px",
                    lineHeight: "16px",
                    letterSpacing: "0.3px",
                    textAlign: "center",
                  }}
                >
                  {feature.description}
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer Action Buttons */}
        <div className="qadpt-tours-actions" style={{ padding: "10px 15px" }}>
          <button
            onClick={handleNextClick}
            disabled={!selectedStepType}
            style={{
              padding: "8px 32px",
              backgroundColor: "#5F9EA0",
              opacity:selectedStepType?"1":"0.5",
              color: "#fff",
              borderRadius: "7px",
              fontSize: "14px",
              fontWeight: 500,
              // cursor: selectedStepType ? "pointer" : "not-allowed",
              transition: "background-color 0.3s ease",
              border: "none",
              cursor:"pointer",
            }}
          >
            NEXT
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeatureSelectionModal;