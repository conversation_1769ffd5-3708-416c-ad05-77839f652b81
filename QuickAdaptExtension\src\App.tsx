import React from 'react';
import logo from './logo.svg';
import "./App.scss";
import GuidePopup from "./components/guideSetting/GuidePopUp";
import Drawer from "./components/drawer/Drawer";
import { AuthProvider } from "./components/auth/AuthProvider";
import { AccountProvider } from "./components/login/AccountContext";
import { SnackbarProvider } from "./components/guideSetting/guideList/SnackbarContext";
import Rte from "./components/guideSetting/RTE";
import jwtDecode from "jwt-decode";
import useInfoStore from "./store/UserInfoStore";

function App() {
	const accessToken = useInfoStore((state) => state.accessToken);
	const { clearAll, clearAccessToken } = useInfoStore.getState();
	if (accessToken) {
		const decodedToken: any = jwtDecode(accessToken);
		const currentTime = Math.floor(Date.now() / 1000);
		if (decodedToken.exp < currentTime) {
			clearAll();
			clearAccessToken();
		}
	}

	return (
		<div className="App">
			<AuthProvider>
				<AccountProvider>
					<SnackbarProvider>
						<Drawer />
					</SnackbarProvider>
				</AccountProvider>
			</AuthProvider>
		</div>
	);
}

export default App;
