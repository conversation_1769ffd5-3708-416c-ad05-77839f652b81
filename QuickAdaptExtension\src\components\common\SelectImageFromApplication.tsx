import React, { useEffect, useState } from "react";
import { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip } from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import Modal from '@mui/material/Modal';

import DriveFolderUploadIcon from '@mui/icons-material/DriveFolderUpload';
import BackupIcon from '@mui/icons-material/Backup';
import { getAllFiles } from "../../services/FileService";
import { FileUpload } from "../../models/FileUpload";



const SelectImageFromApplication = ({ isOpen, handleModelClose, onImageSelect, handleImageUpload, setFormOfUpload, formOfUpload,handleReplaceImage, isReplaceImage }: any) => {

	const [files, setFiles] = useState<FileUpload[]>([]);
	

	const getAllFilesData = async () => {
		try {
			const data = await getAllFiles();
			if (data) {
				const uploads: any = data.map((file:any) => ({
					ImageId: file.Id, 
					FileName: file.Name || null,
					Url: file.Url || '',
				}));
				setFiles(uploads);
			}else{
			}
		} catch (error) {
		}
	}

	useEffect(() => {
		getAllFilesData();
	}, []);

	return (<>
		
		<Modal open={isOpen} onClose={handleModelClose}>
			<Box sx={{
						position: "absolute",
						top: "50%",
						left: "50%",
						transform: "translate(-50%, -50%)",
						width: 450,
						bgcolor: "background.paper",
						boxShadow: 24,
						p: 4,
						maxHeight: "400px",
						overflow: "auto"
					}}>
			
			
				
					<Box
					
				>
					<Typography variant="h6">Select a File</Typography>
					<Box sx={{ display: "flex", flexWrap: "wrap" }}>
						{
							files && (
								files.map((file) => {
									return (
										<Box sx={{ width: "100px", height: "100px", mx: 2 }} >
											<img src={file?.Url} style={{ width: "inherit" }} onClick={() => { onImageSelect(file) }} />
										</Box>
									);
								})
							)
						}
					</Box>
          
					</Box>
				
				
			</Box>
		</Modal>

	</>);
}


export default SelectImageFromApplication;