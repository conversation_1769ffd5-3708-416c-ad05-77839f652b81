import React, { useState, useEffect } from "react";
import { Box, Button, Typography, IconButton, DialogActions } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { CustomIconButton } from "./Button";
import useDrawerStore from "../../store/drawerStore";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import parse, { domToReact } from "html-react-parser";
import { Element } from "domhandler";
import { IconButtonSX } from "./Banner.style";

const BannerEndUser = ({ showBannerenduser, setShowBannerenduser, initialGuideData, backgroundC,currentStep }: any) => {
	const {
		btnBgColor,
		btnTextColor,
		btnBorderColor,
	} = useDrawerStore((state) => state);
	const [showBanner, setShowBanner] = useState(true);
	const { setImageSrc, imageSrc, htmlContent, sectionColor } = useDrawerStore((state: any) => state);
	const Teext = initialGuideData?.GuideStep?.[0]?.TextFieldProperties.Text;
	// const bannerSteps = initialGuideData.GuideStep.filter((step:any) => step.StepType === "Banner");

	const renderHtmlSnippet = (snippet: string | undefined | null) => {
		if (!snippet) return "Sample Text..."; // Return an empty string if snippet is null or undefined.
		return snippet.replace(/(<a\s+[^>]*href=")([^"]*)("[^>]*>)/g, (match, p1, p2, p3) => {
			return `${p1}${p2}" target="_blank"${p3}`;
		});
	};
	const image = imageSrc;
	const isBase64 = (url: string) => url.startsWith("data:image/");
	const textField = initialGuideData?.GuideStep?.[0]?.TextFieldProperties?.[0] || {};
	const { Text: textFieldText, Alignment, Hyperlink, Emoji, TextProperties } = textField;
	const { Bold, Italic, TextColor } = TextProperties || {};
	const customButton =
    initialGuideData?.GuideStep?.[0]?.ButtonSection
        ?.map((section: any) =>
            section.CustomButtons.map((button: any) => ({
                ...button,
                ContainerId: section.Id, // Attach the container ID for grouping
            }))
        )
        ?.reduce((acc: any[], curr: any[]) => acc.concat(curr), []) || [];

	const handleButtonClick = (action: any) => {
		if (action.Action === "open-url" || action.Action === "openurl" || action.Action === "open") {
		if (action.ActionValue === "same-tab") {
		window.location.href = action.TargetUrl;
		} else {
		window.open(action.TargetUrl, "_blank", "noopener noreferrer");
		}
		//onContinue();
		} else if (action.Action === "start-interaction") {
			// onContinue();
			// setOverlayValue(false);
		} else if (action.Action === "close") {
			// onClose();
			// setOverlayValue(false);
		} else if (action.Action === "Next" || action.Action === "next") {
			// Handle next step navigation
			if (typeof currentStep === 'number' && initialGuideData?.GuideStep?.length > currentStep) {
				// Navigate to next step logic would go here
			}
		} else if (action.Action === "Previous" || action.Action === "previous") {
			// Handle previous step navigation
			if (typeof currentStep === 'number' && currentStep > 1) {
				// Navigate to previous step logic would go here
			}
		} else if (action.Action === "Restart") {
			// Reset to the first step
			// Navigate to first step logic would go here
			if (initialGuideData?.GuideStep?.length > 0) {
				// Reset to step 1
				// If there's a specific URL for the first step, navigate to it
				if (initialGuideData.GuideStep[0]?.ElementPath) {
					const firstStepElement = document.evaluate(
						initialGuideData.GuideStep[0].ElementPath,
						document,
						null,
						XPathResult.FIRST_ORDERED_NODE_TYPE,
						null
					).singleNodeValue;

					if (firstStepElement) {
						(firstStepElement as HTMLElement).scrollIntoView({ behavior: 'smooth' });
					}
				}
			}
		} else if (action === undefined || null) {
			// onClose();
			// setOverlayValue(false);
		} else {
			// onClose();
			// setOverlayValue(false);
		}
	};
	const designProps = initialGuideData?.GuideStep?.[0]?.Design || {};
	const IconColor = designProps.IconColor || "#000";
	const IconOpacity = designProps.QuietIcon ? 0.5 : 1.0;
	const canvas = initialGuideData?.GuideStep?.[0]?.Canvas || {};
	const BackgroundColor = canvas?.BackgroundColor	|| "#f1f1f7";
	const Width = canvas.Width || "100%";
	const Radius = canvas.Radius || "0";
	const Padding = canvas.Padding || "10";
	const BorderSize = canvas.BorderSize || "2";
	const BorderColor = canvas.BorderColor || "#f1f1f7";
	const Position =  "absolute";
	const zindex = canvas.Zindex || "999999";
	const Modal = initialGuideData?.GuideStep?.[0]?.Modal;
	const isCloseDisabled = Modal?.DismissOption ?? false;
	const Design = initialGuideData?.GuideStep?.[0]?.Design || {};
	const htmlSnippet = initialGuideData?.GuideStep?.[0]?.HtmlSnippet || "";

	// Apply overflow hidden to body when canvas position is "Cover Top"
	useEffect(() => {
		if (canvas?.Position === "Cover Top") {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "";
		}

		// Cleanup function to restore overflow when component unmounts
		return () => {
			document.body.style.overflow = "";
		};
	}, [canvas?.Position]); // Re-run when canvas position changes

	const countLinesFromHtml = (html: string) => {
		const paragraphCount = (html.match(/<p>/g) || []).length;

		const brCount = (html.match(/<br\s*\/?>/g) || []).length;

		return paragraphCount>0 ? paragraphCount-1 : 0;
	};

	// const renderHtmlSnippet = (snippet: string) => {
	// 	return parse(snippet, {
	// 		replace: (domNode: any) => {
	// 			if (domNode.name === "font") {
	// 				const { size, color, ...otherAttributes } = domNode.attribs || {};
	// 				const fontSize = size ? `${parseInt(size, 10) * 5}px` : "inherit";
	// 				return (
	// 					<span style={{ fontSize, color: color || "inherit", ...otherAttributes }}>
	// 						{domToReact(domNode.children)}
	// 					</span>
	// 				);
	// 			}
	// 			return undefined;
	// 		},
	// 	});
	// };

	return (
		<Box sx={{
			// position: "relative",
			// top: "55px"
		}} className="qadpt-container">
			{showBanner && (
				<Box
					className="qadpt-boxpre"
					id="guide-popup"
					sx={{
						// position: "relative",
						// top: "55px",
						// ...BannerWrapper,
						//top: "55px",//customButton && customButton.length > 0 ? "87px" : "82px",
						left: "50%",
						height: "auto",
						marginTop: `${16+parseInt(Padding||0)+parseInt(BorderSize||0)+(customButton && customButton.length > 0 ? 4 : 0)+(countLinesFromHtml(textFieldText)*10)}px`,//"29px",
						transform: "translate(-50%, -50%)",
						backgroundColor: sectionColor,
						//width: Width,
						maxWidth:"100%",
						//borderRadius: Radius,
						padding: `${Padding}px`,
						//borderWidth: "2px",
						//border: `${BorderSize}px solid ${BorderColor}`,
						boxShadow: Object.keys(canvas).length == 0? "0px 1px 15px rgba(0, 0, 0, 0.7)" : canvas?.Position == "Cover Top" ? "0px 1px 15px rgba(0, 0, 0, 0.7)" : "none",//(canvas.toString.length == 0 || canvas?.Position === "Cover Top") ? "0px 1px 15px rgba(0, 0, 0, 0.7)" : "none",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position
						position: Position,
						zIndex: zindex,

						background:  `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important` ,
						// border: `${BorderSize}px solid ${BorderColor}`,
						borderTop:
							 `${BorderSize}px solid ${BorderColor} !important`
								,
								borderRight:
								 `${BorderSize}px solid ${BorderColor} !important`
								,
								borderLeft:
						  `${BorderSize}px solid ${BorderColor} !important`,

					borderBottom: `${BorderSize}px solid ${BorderColor} !important`,
					}}
				>
					<Box className="qadpt-row" sx={{  display: "flex", alignItems: "center"}}>
					<Box
						sx={{
							// margin: "8px ",
							//bottom: "45px",
							position: "relative",
							display: "flex",
							alignItems: "center",
							placeContent: "center",
								width: "100%",


							"& .MuiTypography-root": {
								width: "100%",
								margin: "0",
							},

						}}
					>
						{/* Display hyperlink  */}
						{Hyperlink ? (
							<Typography
								component="a"
								href={Hyperlink}
								target="_blank" // Open link in a new tab
								rel="noopener noreferrer" // Security measure when using target="_blank"
								sx={{
									color: TextColor,
									padding: "5px 2px",
									textAlign: Alignment || "center",
									marginTop: 1,
									textDecoration: "underline",
								}}
								dangerouslySetInnerHTML={{ __html: renderHtmlSnippet(htmlSnippet) }}
							></Typography>
							) : (

								<Typography
								  className="qadpt-preview qadpt-rte"
								  sx={{
									color: TextColor,
									textAlign: Alignment,
									marginTop: 1,
									whiteSpace: "pre-wrap",
									padding: "5px 2px",
									wordBreak: "break-word",
									"& p": {
									  margin: "0",
									},
								  }}
								  dangerouslySetInnerHTML={{ __html: renderHtmlSnippet(textFieldText) }}
								/>


						)}
						{Emoji && (
							<Typography
								component="span"
								sx={{
									fontWeight: Bold ? "bold" : "normal",
									padding: "5px 2px",
									fontStyle: Italic ? "italic" : "normal",
									color: TextColor,
									textAlign: Alignment ? Alignment : "center",
									mt: 1,
								}}
							>
								{Emoji}
							</Typography>
						)}
						{customButton &&
							customButton.some((button: any) => button.ButtonName && button.ButtonName.trim() !== "") && (
								<DialogActions
									sx={{
									justifyContent: "center",
										padding: "0 !important",
									height: "40px"
									}}
								>
									{customButton.map((button: any, index: any) => (
										<Button
											key={button.Id || `button-${index}`}
											onClick={() => handleButtonClick(button.ButtonAction)}
											variant="contained"
											style={{
												backgroundColor: button.ButtonProperties?.ButtonBackgroundColor,
												color: button.ButtonProperties?.ButtonTextColor,
												border: button.ButtonProperties?.ButtonBorderColor
													? `2px solid ${button.ButtonProperties.ButtonBorderColor}`
													: "none",
												margin: "0 5px",
												fontSize: button.ButtonProperties?.FontSize || 15,
												width: button.ButtonProperties?.Width || "auto",
												padding: "6px 16px",
												textTransform: "none",
												borderRadius: "20px",
												lineHeight: "22px",
											}}
											sx={{
												"&:hover": {
													filter: "brightness(1.2)",
												},
											}}
										>
											{button.ButtonName}
										</Button>
									))}
								</DialogActions>
							)}

						{/* {CustomButton && CustomButton.ButtonName && (
							<Button
								variant="contained"
								sx={{
									fontSize: CustomButton.ButtonProperties?.FontSize || 14,
									width: CustomButton.ButtonProperties?.Width || "auto",
									padding: CustomButton.ButtonProperties?.Padding || "10px",
									color: CustomButton.ButtonProperties?.ButtonTextColor,
									backgroundColor: CustomButton.ButtonProperties?.ButtonBackgroundColor,
									//textAlign: CustomButton.Alignment || "center",
									//display: "block",
									margin: "8px",
									lineHeight: "0px",
									borderRadius: "10px",
									mx: CustomButton.Alignment === "center" ? "auto" : undefined,
								}}
								onClick={handleButtonClick}
							>
								{CustomButton.ButtonName}
							</Button>
						)} */}

						{Hyperlink && (
							<Typography
								component="a"
								href={Hyperlink}
								target="_blank"
								rel="noopener noreferrer"
								sx={{
									color: TextColor,
									textAlign: Alignment || "left",
									mt: 1,
									textDecoration: "underline",
								}}
							>
								{renderHtmlSnippet(textFieldText)}
							</Typography>
						)}

						{image && (
							<Box
								component="a"
								href={initialGuideData.GuideStep[0]?.ImageProperties?.Hyperlink || "#"}
								target="_blank"
								rel="noopener noreferrer"
								sx={{ textAlign: Alignment || "center" }}
							>
								<Box
									component="img"
									src={isBase64(image) ? image : image}
									sx={{
										maxHeight: initialGuideData.GuideStep[0]?.ImageProperties?.MaxImageHeight || "auto",
										objectFit: initialGuideData.GuideStep[0]?.ImageProperties?.UploadedImages?.[0]?.Fit || "contain",
										display: "block",
										margin: "0 auto",
									}}
								/>
							</Box>
						)}
					</Box>{" "}
					{isCloseDisabled && (
						<IconButton
						sx={{
							// position: "fixed",
					boxShadow: "rgba(0, 0, 0, 0.15) 0px 4px 8px",
					marginLeft: "2px",
					background: "#fff !important",
					border: "1px solid #ccc",
					zIndex:"999999",
						borderRadius: "50px",
					padding:"3px !important"

						}}
						>
							<CloseIcon  sx={{zoom:"1",color:"#000"}}   />
						</IconButton>
						)}
					</Box>
				</Box>
			)}
		</Box>
	);
};

export default BannerEndUser;
