import React, { useEffect, useState } from "react";
import { Box, IconButton, Select, MenuItem, FormControl, Button, Typography, Popover, Tooltip, TextField } from "@mui/material";
import { bold, italic, underline, strikeout, ClearFormat, deleteicon,copyicon } from "../../assets/icons/icons";
import { fonts } from "./Fonts";
import {
	FormatAlignLeft,
	FormatAlignCenter,
	FormatAlignRight,
	ArrowDropDown,
	Title,
	FormatListNumbered,
	FormatListBulleted,
	Link,
	VideoLibrary,
	FormatColorText,
	FormatColorFill,
} from "@mui/icons-material";
import { ChromePicker } from "react-color";
import CustomTextField from "../reusable/InputField";
import useDrawerStore from "../../store/drawerStore";
interface RTEProps {
	handleDeleteSection: ()=>void;
	anchorEl: HTMLElement | null;
	onClose: () => void;
	contentEditableRef: React.RefObject<HTMLDivElement>;
	isRTEBanner: boolean;
	showEmojiPicker: boolean;
	savedRange?: Range;
	setSaveRange: (params: Range) => void;
	handleDeleteRTESection: (params: number) => void;
	index: number;
	isPopoverOpen: boolean;
	anchorPosition: { top: number; left: number };
}

const RTE: React.FC<RTEProps> = ({
	handleDeleteSection,
	anchorEl,
	onClose,
	contentEditableRef,
	isRTEBanner,
	savedRange,
	setSaveRange,
	showEmojiPicker,
	handleDeleteRTESection,
	index,
	anchorPosition,
	// isPopoverOpen,
}) => {
	const {
		
		cloneRTEContainer,
		rteAnchorEl,
		currentStep
		
	} = useDrawerStore((state:any) => state);
	const [fontSize, setFontSize] = useState(14);
	const [showSecondRow, setShowSecondRow] = useState(false);
	const [fontFamily, setFontFamily] = useState("Poppins, sans-serif");
	const [alignment, setAlignment] = useState("justifyLeft");
	const [heading, setHeading] = useState("format");
	const [selectedHyperLinkText, setSelectedHyperLinkText] = useState("");
	const [hyperLinkTextName, setHyperLinkTextName] = useState("");
	const [openHyperLink, setOpenHyperLink] = useState(false);
	const [textColor, setTextColor] = useState("#000000");
	const [highlightColor, setHighlightColor] = useState("#FFFF00");
	const [textColorAnchorEl, setTextColorAnchorEl] = useState<HTMLElement | null>(null);
	const [anchorHyperEl, setAnchorHyperEl] = useState<HTMLElement | null>(null);
	const [highlightColorAnchorEl, setHighlightColorAnchorEl] = useState<HTMLElement | null>(null);
	const [isHyperLinkActive, setIsHyperLinkActive] = useState(false);
	const { sectionColor, setSectionColor, selectedTemplate, isPopoverOpen, setIsPopoverOpen } = useDrawerStore(
		(state) => state
	);

	const getFontSize = (fontSize: number) => {
		if (fontSize <= 10) return "1";
		if (fontSize <= 12) return "2";
		if (fontSize <= 14) return "3";
		if (fontSize <= 16) return "4";
		if (fontSize <= 18) return "5";
		if (fontSize <= 20) return "6";
		return "7";
	};
	const [previousStep, setPreviousStep] = useState(currentStep);

	useEffect(() => {
		if (anchorHyperEl === null) {
		  setIsHyperLinkActive(false);
		  setHyperLinkTextName("");
		  setOpenHyperLink(true);
		  setPreviousStep(currentStep);
		  handleClearFormat();
	  }
	}, [currentStep, previousStep,anchorHyperEl]);
	const handleListCommand = (command: string) => {
		const selection = window.getSelection();
		if (!selection || selection.rangeCount === 0) return;

		const range = selection.getRangeAt(0);
		
		// Check if the selected text is already in a list
		let currentElement: Node | null = range.startContainer;
		if (currentElement && currentElement.nodeType === Node.TEXT_NODE) {
			currentElement = (currentElement as Text).parentElement;
		}
		
		// Find if we're already inside a list
		let listParent: Element | null = null;
		if (currentElement) {
			let tempElement: Element | null = currentElement as Element;
			while (tempElement && tempElement !== contentEditableRef.current) {
				if (tempElement.tagName === 'OL' || tempElement.tagName === 'UL') {
					listParent = tempElement;
					break;
				}
				tempElement = tempElement.parentElement;
			}
		}
		
		// If already in a list, remove list formatting
		if (listParent) {
			const listItems = Array.from(listParent.children);
			const textContent = listItems.map(item => item.textContent).join('\n');
			
			// Create a text node with the content
			const textNode = document.createTextNode(textContent);
			listParent.parentNode?.replaceChild(textNode, listParent);
			
			// Clear selection and focus
			selection.removeAllRanges();
			if (contentEditableRef.current) {
				contentEditableRef.current.focus();
			}
			return;
		}
		
		// If there's no selection, expand to the current line
		if (range.collapsed) {
			const textNode = range.startContainer;
			const textContent = textNode.textContent || '';
			const lineStart = textContent.lastIndexOf('\n', range.startOffset - 1) + 1;
			const lineEnd = textContent.indexOf('\n', range.startOffset);
			
			range.setStart(textNode, lineStart);
			range.setEnd(textNode, lineEnd === -1 ? textContent.length : lineEnd);
		}

		// Get the selected text content
		const selectedText = range.toString();
		
		if (selectedText.trim()) {
			// Split selected text into lines, preserving empty lines as space
			const lines = selectedText.split('\n').filter(line => line.trim() !== '');
			
			// Create list items
			const listType = command === "insertOrderedList" ? "ol" : "ul";
			const listElement = document.createElement(listType);
			
			// Add some basic styling to match the editor's appearance
			listElement.style.margin = "16px 0";
			listElement.style.paddingLeft = "20px";
			
			lines.forEach(line => {
				const listItem = document.createElement("li");
				listItem.textContent = line.trim();
				listItem.style.margin = "4px 0";
				listElement.appendChild(listItem);
			});
			
			// Replace the selected content with the list
			range.deleteContents();
			range.insertNode(listElement);
			
			// Add a line break after the list for better formatting
			const lineBreak = document.createElement("br");
			listElement.parentNode?.insertBefore(lineBreak, listElement.nextSibling);
			
			// Clear selection
			selection.removeAllRanges();
			
			// Focus back to content editable
			if (contentEditableRef.current) {
				contentEditableRef.current.focus();
			}
		}
	};

	const applyCommand = (command: string, value: string | undefined = undefined) => {
		if (contentEditableRef.current) {
			if (command === "fontSize") {
				//contentEditableRef.current.focus();
				const fS = getFontSize(value ? +value : 10);
				document.execCommand(command, false, fS);
			} else if (command === "em") {
				const selection = window.getSelection();
				const range = selection?.getRangeAt(0); // Get selected range
				const selectedText = selection?.toString();

				if (selectedText && range) {
					let parentElement = range.startContainer.parentElement;

					// Traverse up to find the closest <em> ancestor
					while (parentElement && parentElement.tagName !== "EM") {
						parentElement = parentElement.parentElement;
					}

					if (parentElement && parentElement.tagName === "EM") {
						// If already wrapped in <em>, remove it but preserve other styles
						const emParent = parentElement;
						const grandParent = emParent.parentNode;

						if (grandParent) {
							// Move all child nodes of <em> to its parent (preserving other styles)
							while (emParent.firstChild) {
								grandParent.insertBefore(emParent.firstChild, emParent);
							}

							// Remove the now-empty <em> tag
							grandParent.removeChild(emParent);
						}
					} else {
						// If not wrapped in <em>, apply <em> (italic) while preserving other styles
						const emElement = document.createElement("em");
						const selectedContent = range?.extractContents(); // Extract the content to preserve other styles

						if (selectedContent) {
							// Insert the extracted content inside the <em> element
							emElement.appendChild(selectedContent);

							// Insert the <em> element back into the range's start position
							range.insertNode(emElement);

							// Re-select the inserted <em> element
							const newRange = document.createRange();
							newRange.selectNode(emElement);
							selection?.removeAllRanges();
							selection?.addRange(newRange);
						}
					}
				}
			} else if (command === "foreColor" || command === "hiliteColor") {
				if (savedRange) {
					const selection = window.getSelection();
					selection?.removeAllRanges();
					selection?.addRange(savedRange);
					document.execCommand(command, false, value);
				}
			} else if (command === "bold-foreColor") {
				if (savedRange) {
					const selection = window.getSelection();
					selection?.removeAllRanges();
					selection?.addRange(savedRange);
					document.execCommand("createLink", false, hyperLinkTextName);
					document.execCommand("bold", false);
					document.execCommand("foreColor", false, value);
				}
			} else if (command === "insertOrderedList" || command === "insertUnorderedList") {
				// Handle list operations for selected text only
				handleListCommand(command);
			} else {
				// contentEditableRef.current.focus();
				document.execCommand(command, false, value);
			}
		}
	};

	const handleFontSizeChange = (action: "increase" | "decrease") => {
		const newSize = action === "increase" ? fontSize + 2 : fontSize - 2;
		if (newSize >= 10 && newSize <= 20) {
			setFontSize(newSize);
			applyCommand("fontSize", String(newSize));
		}
	};

	const handleAlignmentChange = (event: any) => {
		const selectedAlignment = event.target.value;
		setAlignment(selectedAlignment);
		applyCommand(selectedAlignment);
	};

	const handleHeadingChange = (event: any) => {
		const selectedHeading = event.target.value;
		setHeading(selectedHeading);
		applyCommand("formatBlock", selectedHeading);
	};

	const handleToggleSecondRow = () => {
		setShowSecondRow(!showSecondRow);
	};
	const handleTextColorClick = (event: React.MouseEvent<HTMLElement>) => {
		const selection = window.getSelection();
		if (selection && selection.rangeCount > 0) {
			setSaveRange(selection.getRangeAt(0));
		}
		setTextColorAnchorEl(event.currentTarget);
	};
	const handleCloneContainer = () => {
		cloneRTEContainer(rteAnchorEl.containerId);
	};


	const handleHighlightColorClick = (event: React.MouseEvent<HTMLElement>) => {
		const selection = window.getSelection();
		if (selection && selection.rangeCount > 0) {
			setSaveRange(selection.getRangeAt(0));
		}
		setHighlightColorAnchorEl(event.currentTarget);
	};
	const isDisabled = true;
	const handleCloseTextColorPopover = () => {
		setTextColorAnchorEl(null);
	};

	const handleCloseHighlightColorPopover = () => {
		setHighlightColorAnchorEl(null);
	};

	const handleClickHyperLink = (event: React.MouseEvent<HTMLElement>) => {
		const selection = window.getSelection();
		if (selection && selection.rangeCount > 0) {
			let range = selection.getRangeAt(0);
			let selectedText = selection.toString();
			if (!selectedText) {
				range = expandRangeToWord(range);
				selectedText = range.toString();
			}
			if (selectedText) {
				setSaveRange(range.cloneRange());
				setAnchorHyperEl(event.currentTarget);
				setOpenHyperLink(true);
				setIsHyperLinkActive(true); // Set hyperlink as active after adding
			}
		} else {
			setOpenHyperLink(false);
		}
	};

	const expandRangeToWord = (range: Range): Range => {
		const text = range.startContainer.textContent || "";
		let start = range.startOffset;
		let end = range.endOffset;

		while (start > 0 && /\S/.test(text[start - 1])) {
			start--;
		}
		while (end < text.length && /\S/.test(text[end])) {
			end++;
		}

		range.setStart(range.startContainer, start);
		range.setEnd(range.startContainer, end);

		return range;
	};

	const removeHyperLink = () => {
		if (contentEditableRef.current && savedRange) {
			const selection = window.getSelection();
			selection?.removeAllRanges();
			selection?.addRange(savedRange);
			document.execCommand("unlink", false);
			document.execCommand("foreColor", false, "#000000");
		}
		setHyperLinkTextName("");
		setIsHyperLinkActive(false);
	};

	const handleChangeHyperLink = (event: React.ChangeEvent<HTMLInputElement>) => {
		setHyperLinkTextName(event.target.value);
	};

	useEffect(() => {
		if (hyperLinkTextName) {
			applyCommand("bold-foreColor", "#0000EE");
		}
	}, [anchorHyperEl]);

	const handleClearFormat = () => {
		setTextColor("#000000");
		setFontSize(14);
		setHighlightColor("#FFFF00");
		setFontFamily("Poppins, sans-serif");
		setAlignment("justifyLeft");
		setHeading("format");
		applyCommand("removeFormat");
		removeHyperLink();
	};

	return (
		<>
			<Popover
				open={isPopoverOpen}
				// anchorEl={anchorEl}
				onClose={() => {
					setAnchorHyperEl(null);
					onClose();
				}}
				disableEnforceFocus
				disableAutoFocus
				anchorReference={"anchorPosition"}
				anchorPosition={anchorPosition && { top: anchorPosition.top, left: anchorPosition.left }}
				// anchorOrigin={{
				// 	vertical: "top",
				// 	horizontal: "center",
				// }}
				// transformOrigin={{
				// 	vertical: "bottom",
				// 	horizontal: "center",
				// }}
				PaperProps={{
					sx: {
						padding: "5px",
						display: "flex",
						flexDirection: "column",
						alignItems: "center",
						justifyContent: "space-between",
						marginTop: isRTEBanner ? "80px" : "-40px",
					},
				}}
			>
				<Box
					sx={{
						gap: "8px",
						display: "flex",
						alignItems: "center",
						placeContent: "center",
					}}
				>
					<Tooltip
						title="Font Style"
						placement="top"
					>
						<TextField
							value="Poppins"
							variant="outlined"
							size="small"
							InputProps={{
								readOnly: true, // Makes the textbox non-editable
								sx: {
									height: "32px",
									"& .MuiOutlinedInput-notchedOutline": {
										border: "none",
									},
									padding: "4px 8px",
									fontFamily: "Poppins, sans-serif", // Applies the font style
								},
							}}
							sx={{
								width: "100px", // Explicitly sets the width
								minWidth: "100px",
								maxWidth: "100px", // Prevents the TextField from expanding
							}}
						/>
					</Tooltip>

					{/* <FormControl
						variant="outlined"
						size="small"
						sx={{ minWidth: "100px" }}
					>
						<Select
							value={fontFamily}
							onChange={(e) => {
								setFontFamily(e.target.value);
								applyCommand("fontName", e.target.value as string);
							}}
							displayEmpty
							sx={{
								height: "32px",
								"& .MuiOutlinedInput-notchedOutline": {
									border: "none",
								},
								"& .MuiSelect-select": {
									padding: "4px 8px",
								},
							}}
						>
							{fonts.map((font) => (
								<MenuItem
									key={font.value}
									value={font.value}
								>
									{font.label}
								</MenuItem>
							))}
						</Select>
					</FormControl> */}

					<Box
						display="flex"
						alignItems="center"
						sx={{ gap: "1px" }}
					>
						<Button
							size="small"
							onClick={() => handleFontSizeChange("decrease")}
							sx={{
								minWidth: "10px !important",
								padding: "0px !important",
							}}
						>
							-
						</Button>
						<Typography
							variant="body2"
							sx={{ padding: "4px 8px", fontSize: "16px" }}
						>
							{fontSize}
						</Typography>
						<Button
							size="small"
							onClick={() => handleFontSizeChange("increase")}
							sx={{
								minWidth: "10px !important",
								padding: "0px !important",
							}}
						>
							+
						</Button>
					</Box>
					<Tooltip title="Bold">
						<IconButton
							onClick={() => applyCommand("bold")}
							sx={{ borderRadius: "6px" }}
						>
							<span dangerouslySetInnerHTML={{ __html: bold }} />
						</IconButton>
					</Tooltip>
					<Tooltip title="Italic">
						<IconButton
							onClick={() => applyCommand("em")}
							sx={{ borderRadius: "6px" }}
						>
							<span dangerouslySetInnerHTML={{ __html: italic }} />
						</IconButton>
					</Tooltip>
					<Tooltip title="Underline">
						<IconButton
							onClick={() => applyCommand("underline")}
							sx={{ borderRadius: "6px" }}
						>
							<span dangerouslySetInnerHTML={{ __html: underline }} />
						</IconButton>
					</Tooltip>
					<Tooltip title="StrikeThrough">
						<IconButton
							onClick={() => applyCommand("strikeThrough")}
							sx={{ borderRadius: "6px" }}
						>
							<span dangerouslySetInnerHTML={{ __html: strikeout }} />
						</IconButton>
					</Tooltip>

					<FormControl
						variant="outlined"
						size="small"
						sx={{ minWidth: "80px" }}
					>
						<Tooltip
							title="Alignment"
							placement="top"
						>
							<Select
								value={alignment}
								onChange={handleAlignmentChange}
								displayEmpty
								sx={{
									height: "32px",
									"& .MuiOutlinedInput-notchedOutline": {
										border: "none",
									},
									"& .MuiSelect-select": {
										padding: "4px 8px",
									},
								}}
							>
								<MenuItem value="justifyLeft">
									<FormatAlignLeft /> Left
								</MenuItem>
								<MenuItem value="justifyCenter">
									<FormatAlignCenter /> Center
								</MenuItem>
								<MenuItem value="justifyRight">
									<FormatAlignRight /> Right
								</MenuItem>
							</Select>
						</Tooltip>
					</FormControl>
					<Tooltip title="More">
						<IconButton
							onClick={handleToggleSecondRow}
							sx={{ borderRadius: "6px" }}
						>
							<ArrowDropDown />
						</IconButton>
					</Tooltip>
				</Box>

				{showSecondRow && (
					<Box
						display="flex"
						width="100%"
						alignItems="center"
						sx={{ gap: "6px", borderRadius: "6px", mt: "8px" }}
					>
						<Tooltip title="Text Color">
							<IconButton
								onClick={handleTextColorClick}
								sx={{ borderRadius: "6px" }}
							>
								<FormatColorText />
							</IconButton>
						</Tooltip>
						<Popover
							open={Boolean(textColorAnchorEl)}
							anchorEl={textColorAnchorEl}
							onClose={handleCloseTextColorPopover}
							anchorOrigin={{
								vertical: "bottom",
								horizontal: "center",
							}}
							transformOrigin={{
								vertical: "bottom",
								horizontal: "center",
							}}
							sx={{
								zIndex: 9999999,
							}}
						>
							<ChromePicker
								color={textColor}
								onChangeComplete={(color) => {
									setTextColor(color.hex);
									applyCommand("foreColor", color.hex);
								}}
							/>
						</Popover>
						<Tooltip title="Text Highlight">
							<IconButton
								onClick={handleHighlightColorClick}
								sx={{ borderRadius: "6px" }}
							>
								<FormatColorFill />
							</IconButton>
						</Tooltip>
						<Popover
							open={Boolean(highlightColorAnchorEl)}
							anchorEl={highlightColorAnchorEl}
							onClose={handleCloseHighlightColorPopover}
							anchorOrigin={{
								vertical: "bottom",
								horizontal: "center",
							}}
							transformOrigin={{
								vertical: "bottom",
								horizontal: "center",
							}}
							sx={{
								zIndex: 9999999,
							}}
						>
							<ChromePicker
								color={highlightColor}
								onChangeComplete={(color) => {
									setHighlightColor(color.hex);
									applyCommand("hiliteColor", color.hex);
								}}
							/>
						</Popover>
						<Tooltip
							title="Coming Soon"
							PopperProps={{
								sx: {
									zIndex: 9999,
								},
							}}
						>
							<span>
								<FormControl
									variant="outlined"
									size="small"
									sx={{ minWidth: "100px" }}
									disabled
								>
									<Select
										value={heading}
										onChange={handleHeadingChange}
										displayEmpty
										sx={{
											"& .MuiOutlinedInput-notchedOutline": { border: "none" },
											height: "32px",
										}}
									>
										<MenuItem
											value="format"
											disabled
										>
											Format
										</MenuItem>
										<MenuItem value="h1">
											<Title /> Heading 1
										</MenuItem>
										<MenuItem value="h2">
											<Title /> Heading 2
										</MenuItem>
										<MenuItem value="h3">
											<Title />
											Heading 3
										</MenuItem>
									</Select>
								</FormControl>
							</span>
						</Tooltip>
						<Tooltip
							title="Ordered List"
							PopperProps={{
								sx: {
									zIndex: 9999,
								},
							}}
						>
							<IconButton
								onClick={() => applyCommand("insertOrderedList")}
								sx={{ borderRadius: "6px" }}
							>
								<FormatListNumbered />
							</IconButton>
						</Tooltip>
						<Tooltip
							title="Unordered List"
							PopperProps={{
								sx: {
									zIndex: 9999,
								},
							}}
						>
							<IconButton
								onClick={() => applyCommand("insertUnorderedList")}
								sx={{ borderRadius: "6px" }}
							>
								<FormatListBulleted />
							</IconButton>
						</Tooltip>
						<Tooltip title="Hyperlink">
							<IconButton
								sx={{ borderRadius: "6px" }}
								onClick={handleClickHyperLink}
								disabled={isHyperLinkActive}
							>
								<Link />
							</IconButton>
						</Tooltip>
						<Tooltip
							title={isDisabled ? "Coming Soon" : ""} // Hide tooltip content when disabled
							PopperProps={{
								sx: {
									zIndex: 9999,
								},
							}}
							disableInteractive={isDisabled} // Disable tooltip interaction
						>
							<span>
								<IconButton
									sx={{
										borderRadius: "6px",
										pointerEvents: isDisabled ? "none" : "auto", // Disable interactions
										opacity: isDisabled ? 0.5 : 1, // Adjust opacity when disabled
										cursor: isDisabled ? "not-allowed" : "pointer", // Change cursor on hover
									}}
								>
									<VideoLibrary />
								</IconButton>
							</span>
						</Tooltip>
						<Box>
							{/* <Typography className="qadpt-control-label">Section Color</Typography> 

							<Tooltip
								title="Section Color"
								PopperProps={{
									sx: {
										zIndex: 9999,
									},
								}}
							>
								<input
									type="color"
									value={sectionColor}
									onChange={(e) => setSectionColor(e.target.value)}
									className="qadpt-color-input"
								/>
							</Tooltip>
							*/}
						</Box>
						<Tooltip title="Clear Formating">
							<IconButton
								sx={{ borderRadius: "6px" }}
								onClick={handleClearFormat}
							>
								<span dangerouslySetInnerHTML={{ __html: ClearFormat }} />
							</IconButton>
						</Tooltip>
						{!isRTEBanner && (	
						<Tooltip title="Clone">
  <IconButton size="small" onClick={handleCloneContainer}>
    <span dangerouslySetInnerHTML={{ __html: copyicon }} />
  </IconButton>
					</Tooltip>
	)}
				{!isRTEBanner && (
				<Tooltip title="Delete">
					<IconButton size="small" onClick={handleDeleteSection}>
					<span dangerouslySetInnerHTML={{ __html: deleteicon }} />
					</IconButton>
				</Tooltip>
				)}
					</Box>
				)}
			</Popover>
			<Popover
				open={Boolean(anchorHyperEl)}
				anchorEl={anchorHyperEl}
				onClose={() => setAnchorHyperEl(null)}
				disableEnforceFocus
				disableAutoFocus
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "center",
				}}
				transformOrigin={{
					vertical: "bottom",
					horizontal: "left",
				}}
				PaperProps={{
					style: {
						padding: "8px 16px",
						width: "250px",
						gap: "10px",
						marginTop: "55px",
						borderRadius: "4px",
					},
				}}
			>
				{openHyperLink ? (
					<HyperLinkContinaer
						value={hyperLinkTextName}
						handleChangeHyperLink={handleChangeHyperLink}
					/>
				) : null}
			</Popover>
		</>
	);
};

export default RTE;

interface IHyperLinkContinaer {
	value: string;
	handleChangeHyperLink: (event: React.ChangeEvent<HTMLInputElement>) => void;
}
const HyperLinkContinaer = ({ value, handleChangeHyperLink }: IHyperLinkContinaer) => {
	return (
		<CustomTextField
			value={value}
			onChange={handleChangeHyperLink}
			placeholder="This is a text Link"
		/>
	);
};
