import React, { createContext, useContext, useState, useRef, ReactNode, useCallback } from "react";
import { Snackbar, Alert } from "@mui/material";
import CelebrationOutlinedIcon from "@mui/icons-material/CelebrationOutlined";
import ErrorOutlineOutlinedIcon from "@mui/icons-material/ErrorOutlineOutlined";

type SnackbarContextType = {
	openSnackbar: (message: string, severity: "success" | "error") => void;
};

const SnackbarContext = createContext<SnackbarContextType | undefined>(undefined);

export const useSnackbar = () => {
	const context = useContext(SnackbarContext);
	if (!context) {
		throw new Error("useSnackbar must be used within a SnackbarProvider");
	}
	return context;
};

export const SnackbarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState("");
	const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");

  // Add a ref to track snackbar timers
  const snackbarTimerRef = useRef<NodeJS.Timeout | null>(null);
  const closeTimerRef = useRef<NodeJS.Timeout | null>(null);

  const openSnackbar = useCallback((message: string, severity: "success" | "error") => {
    // Clear any existing timers to prevent conflicts
    if (snackbarTimerRef.current) {
      clearTimeout(snackbarTimerRef.current);
      snackbarTimerRef.current = null;
    }

    if (closeTimerRef.current) {
      clearTimeout(closeTimerRef.current);
      closeTimerRef.current = null;
    }

    // First close any existing snackbar
    setSnackbarOpen(false);

    // Then set up the new snackbar with a slight delay
    snackbarTimerRef.current = setTimeout(() => {
      setSnackbarMessage(message);
      setSnackbarSeverity(severity);
      setSnackbarOpen(true);

      // Set up auto-close timer
      closeTimerRef.current = setTimeout(() => {
        setSnackbarOpen(false);
        closeTimerRef.current = null;
      }, 4000);

      snackbarTimerRef.current = null;
    }, 100);
  }, []);

  const handleSnackbarClose = () => {
    if (closeTimerRef.current) {
      clearTimeout(closeTimerRef.current);
      closeTimerRef.current = null;
    }
    setSnackbarOpen(false);
  };

    return (
      <SnackbarContext.Provider value={{ openSnackbar }}>
        {children}
        <Snackbar
          className={`qadpt-toaster ${snackbarSeverity === 'success' ? 'qadpt-toaster-success' : 'qadpt-toaster-error'}`}
          open={snackbarOpen}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <Alert
            onClose={handleSnackbarClose}
            severity={snackbarSeverity}
            className="qadpt-alert"
            icon={<></>}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </SnackbarContext.Provider>
  );
};
