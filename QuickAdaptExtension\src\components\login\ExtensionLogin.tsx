import React, { useState } from 'react';
import { TextField, Button, Typography, IconButton, InputAdornment, FormHelperText } from '@mui/material';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import Visibility from '@mui/icons-material/Visibility';
import { GetUserDetails, GetUserDetailsById, UserLogin } from '../../services/UserService';
import { useAuth } from '../auth/AuthProvider';
import JSEncrypt from 'jsencrypt';
import { LoginService } from '../../services/LoginService';
import { User } from '../../models/User';
import { getOrganizationById } from '../../services/OrganizationService';
import { Padding } from '@mui/icons-material';
import useDrawerStore from "../../store/drawerStore";
import useInfoStore from "../../store/UserInfoStore";
import userSession from "../../store/userSession";
import { pwdeye,eyeclose } from '../../assets/icons/icons';
const { setAccessToken, setOidcInfo, setUser, setOrgDetails, setUserType } = useInfoStore.getState();
const { setHasAnnouncementOpened, hasAnnouncementOpened } = userSession.getState();
const { clearAll, clearAccessToken } = useInfoStore.getState();
const { clearGuideDetails, setActiveMenu, setSearchText } = useDrawerStore.getState();
const {	clearUserSession} = userSession.getState();
let userLocalData: { [key: string]: any } = {}
let SAinitialsData: string;
let userDetails: User;
interface ExtensionLoginProps {
    setIsLoggedIn: (value: boolean) => void;
}
const ExtensionLogin: React.FC<ExtensionLoginProps> = ({ setIsLoggedIn }) => {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loginUserDetails, setUserDetails] = useState<User | null>(null);

  const loginStyles = {
    qadptDrawerContent: {
      width: '100%',
      backgroundColor: 'var(--ext - background)',
      marginTop: '20px',
    },
    qadptWelcomeMessage: {
      fontWeight: "600",
      textAlign: "left" as React.CSSProperties["textAlign"],
      padding: "14px",
    },
        // container: {
    //   padding: '10px',
    //   backgroundColor: '#F6EEEE',
    //   borderRadius: '8px',
    // },
    // welcomeText: {
    //   fontFamily: 'Poppins, sans-serif',
    //   fontSize: '16px',
    //   fontWeight: 600,
    //   lineHeight: '24px',
    //   textAlign: 'left',
    //   color: 'rgba(34, 34, 34, 1)',
    // },
    // headerText: {
    //   fontFamily: 'Poppins, sans-serif',
    //   fontSize: '14px',
    //   fontWeight: 400,
    //   lineHeight: '24px',
    //   textAlign: 'left',
    //   color: 'rgba(68, 68, 68, 1)',
    //   marginTop: '10px',
    // },
    // textField: {
    //   width: '100%',
    //   backgroundColor: 'rgba(255, 255, 255, 1)',
    //   borderRadius: '6px',
    //   height: '46px',
    // },
    // textFieldInput: {
    //   fontFamily: 'Poppins, sans-serif',
    //   fontSize: '16px',
    //   fontWeight: 400,
    //   color: 'rgba(68, 68, 68, 1)',
    //   padding: '12px',
    //   border: '1px solid rgba(213, 213, 213, 1)',
    //   borderRadius: '6px',
    //   height: '46px',
    // },
    // loginButton: {
    //   backgroundColor: '#5F9EA0',
    //   color: '#fff',
    //   borderRadius: '25px',
    //   width: '100%',
    //   marginTop: '20px',
    //   textTransform: 'none',
    //   fontFamily: 'Poppins, sans-serif',
    //   fontSize: '16px',
    //   fontWeight: 500,
    // },
    // qadptTextdanger: {
    //   color: '#d9534f',
    //   fontSize: '0.9rem',
    // },

    qadptLoginForm: {
      marginTop: '20px',
      padding: '0px 10px 20px 10px', 
    },
    qadptFormLabel: {
      fontSize: "14px",
      marginTop: "10px",
      textAlign: "left" as React.CSSProperties["textAlign"],
    },
    qadptInvalidCreds: {
      fontSize: "14px",
    },
        // qadpteyeicon: {
    //   "& .MuiIconButton-root": {
    //     backgroundColor: "transparent !important",
    //     border: "none !important",
    //     padding: "0 !important", // Note: Correct "Padding" to "padding"
    //   }
    // },
    
    qadptForgotPwd: {
      color: "var(--primarycolor)", 
      cursor: "pointer",
      fontSize: "16px",
      fontWeight: "400",
      lineHeight: "24px",
      marginTop: "10px",
      textAlign: "left" as React.CSSProperties["textAlign"],
    },
    qadptBtn: {
      backgroundColor: "var(--primarycolor) !important",  
      color: "#fff",
      border: "none",
      padding: "10px 12px",
      cursor: "pointer",
      fontSize: "16px",
      borderRadius: "12px",
      width: "100%",
      marginTop: "10px",
      lineHeight: "20px",
      textTransform:"none",
      
    } as React.CSSProperties,
  };

  const validateForm = (): boolean => {
    if (!email.trim() && !password.trim()) {
      setError("Email and Password are required.");
      return false;
    }
    if (!email.trim()) {
      setError("Email is required.");
      return false;
    }
    if (!validateEmail(email)) {
      setError("Enter a valid email address.");
      return false;
    }
    if (!password.trim()) {
      setError("Password is required.");
      return false;
    }
    return true;
  };
  
  

  const handleLoginSuccess = async () => {
    try {
      if (!validateForm()) return;
  
      clearAll();
      clearGuideDetails();
      clearUserSession();
      setActiveMenu(null);
      setSearchText("");
  
      const organizationId = "1";
      const rememberLogin = true;
      const returnUrl = "";
      const authType = "admin";
      const tenantId = "web";

      const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';

      const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';
                const encryptor = new JSEncrypt();
                encryptor.setPublicKey(publicKey);
                const now = new Date().toISOString();
                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();
                if (!encryptedPassword) {
                  console.error("Encryption failed");
                  return; 
                }
  
      const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);
      if (response.access_token) {
        setAccessToken(response.access_token);
        setOidcInfo(response);
        const userResponse = await GetUserDetails();
        if (userResponse) {
          setUser(userResponse);
          const firstNameInitials = userResponse?.FirstName?.charAt(0).toUpperCase() || '';
          const lastNameInitials = userResponse?.LastName?.charAt(0).toUpperCase() || '';
          SAinitialsData = firstNameInitials + lastNameInitials;
          setUserType(userResponse?.UserType ?? "");
          const orgDetails = await getOrganizationById(userResponse?.OrganizationId ?? "");
          setOrgDetails(orgDetails);
          setIsLoggedIn(true);
          if (!hasAnnouncementOpened) setHasAnnouncementOpened(true);
        }
      } else {
        setIsLoggedIn(false);
        setError(response.error_description || "Login failed. Please check your credentials.");
      }
    } catch (err) {
      console.error(err);
      setError("An unexpected error occurred. Please try again later.");
    }
  };
  

  const handleClickShowPassword = () => setShowPassword(!showPassword);

  const handlePasswordChange = (event: any) => {
    setPassword(event.target.value);
    setError(null);
  };

  return (
    <div style={loginStyles.qadptDrawerContent}>	
<div style={{ ...loginStyles.qadptWelcomeMessage, fontSize: '16px !important' }}>
  Welcome back
</div>
    <div style={loginStyles.qadptLoginForm}>

        <div style={loginStyles.qadptFormLabel}>Email</div>

        <TextField
            fullWidth
            autoFocus            
            value={email}
            onChange={(e) => {
                setEmail(e.target.value); 
                setError(null); 
              }}
          placeholder="Enter your email"
          sx={{
            "& .MuiInputBase-root":{
              fontSize: "16px !important",
              fontWeight: "400 !important",
              padding: "12px !important",
              border: "1px solid var(--border-color) !important",
              borderRadius: "6px !important",
              boxShadow: "none !important",
              height: "42px !important",
              backgroundColor: "var(--white-color) !important",
              marginTop: "10px !important",
            },
            "& .MuiInputBase-input": {
              height: "34px !important",
              border: "none !important",
              padding:"0 !important",
              
            }
          }}
            InputProps={{										
              // className: "qadpt-input-field",
              disableUnderline: true,
          }}
          variant="standard"
        />

        <div style={loginStyles.qadptFormLabel}>Password</div>
        <TextField
            required
            fullWidth
            type={showPassword ? "text" : "password"}
            id="password"
            name="password"
            autoComplete="password"           
            value={password}
            onChange={handlePasswordChange}
          placeholder="Enter your password"
          sx={{
            "& .MuiInputBase-root":{
              fontSize: "16px !important",
              fontWeight: "400 !important",
              padding: "12px !important",
              border: "1px solid var(--border-color) !important",
              borderRadius: "6px !important",
              boxShadow: "none !important",
              height: "46px !important",
              backgroundColor: "var(--white-color) !important",
              marginTop: "10px !important",
            },
            "& .MuiInputBase-input": {
              height: "34px !important",
              border: "none !important",
              padding:"0 !important",
            }
          }}
            InputProps={{ 
              endAdornment: (
                <InputAdornment 
                position="end" 
               
              >
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleClickShowPassword}
                    edge="end"
                    sx={{
                      backgroundColor: "transparent !important",
                      border: "none !important",
                      padding: "0 !important",
                      margin: "0 !important",
                    }}
                    //style={loginStyles.qadpteyeicon} 
                >
{showPassword ?  <span dangerouslySetInnerHTML={{ __html: pwdeye }}/>: <span dangerouslySetInnerHTML={{ __html: eyeclose }}/>}
</IconButton>
              </InputAdornment>
                    
                ),
               
                disableUnderline: true,
            }}
            variant="standard"
           
        />	
         {error && (
          <FormHelperText error style={loginStyles.qadptInvalidCreds}>
              {error}
          </FormHelperText>
        )}

        <div style={loginStyles.qadptForgotPwd}>
        <span 
        onClick={() => window.open(`${process.env.REACT_APP_WEB_API}/forgotpassword`, '_blank')}
        >
          Forgot password?
        </span>
        </div>

        <Button
        variant="contained"
        onClick={handleLoginSuccess}
          style={loginStyles.qadptBtn}
          
    >
        Log in
    </Button>
    </div>
</div>
  );
};

export default ExtensionLogin;
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

