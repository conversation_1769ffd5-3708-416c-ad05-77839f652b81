import React, { useState, useEffect } from "react";
import { Box, Typography, ToggleButton, ToggleButtonGroup, IconButton, FormControlLabel, Switch, <PERSON>ton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import useDrawerStore, { DrawerState } from "../../store/drawerStore";
import "./Canvas.module.css";

interface OverlaySettingsProps {
	selectedTemplate: string;
	onStatusChange: (status: boolean) => void;
	setOverLays: (status: boolean) => void;
	anchorEl: HTMLElement | null;
	setDesignPopup: (status: boolean) => void;
}

const OverlaySettings = ({
	selectedTemplate,
	onStatusChange,
	setOverLays,
	anchorEl,
	setOverlaySettings,
	setDesignPopup,
	setMenuPopup
}: any) => {
	const [isOpen, setIsOpen] = useState(true);
	const { setOverlayEnabled, overlayEnabled, selectedTemplateTour, setIsUnSavedChanges } = useDrawerStore((state) => state);
	const { pageinteraction, setPageInteraction } = useDrawerStore((state: DrawerState) => state);

	// Initialize local state from store values
	const [interaction, setInteraction] = useState<boolean>(pageinteraction);
	const [status, setStatus] = useState(overlayEnabled);

	// Debug: Log the store values
	console.log('Overlay Component - Store values:', { overlayEnabled, pageinteraction });
	console.log('Overlay Component - Local state:', { status, interaction });

	// Keep local state in sync with store when store values change
	useEffect(() => {
		console.log('Overlay Component - useEffect triggered:', { overlayEnabled, pageinteraction });
		setStatus(overlayEnabled);
		setInteraction(pageinteraction);
	}, [overlayEnabled, pageinteraction]);

	const handleStatusChange = (event: any) => {
		const newStatus = event.target.checked;
		setStatus(newStatus);

		// Implement mutual exclusivity: when overlay changes, page interaction must be opposite
		if (newStatus === false) {
			// When overlay is disabled, automatically enable page interaction
			setInteraction(true);
		} else {
			// When overlay is enabled, automatically disable page interaction
			setInteraction(false);
		}
	};

	const handleApplyChanges = () => {
		// Create a batch update function to record a single history entry
		const batchUpdate = useDrawerStore.getState().batchUpdate;

		// Use the batch update function to record a single history entry
		batchUpdate(
			() => {
				// Apply the changes
				setOverlayEnabled(status);
				setPageInteraction(interaction);
			},
			'OVERLAY_BATCH_UPDATE',
			`Updated overlay settings`
		);

		// Update UI state
		setIsOpen(false);
		setOverlaySettings(false);
		setMenuPopup(true);
		setIsUnSavedChanges(true);
	};

	const handleInteractionChange = (event: any) => {
		const newInteraction = event.target.checked;
		setInteraction(newInteraction);

		// Implement asymmetric mutual exclusivity: only disable overlay when page interaction is enabled
		if (newInteraction === true) {
			// When page interaction is enabled, automatically disable overlay
			setStatus(false);
		}
		// When page interaction is disabled, do NOT automatically enable overlay
		// This allows both options to be disabled simultaneously
	};

	const handleClose = () => {
		setIsOpen(false);
		setOverlaySettings(false);
		setMenuPopup(true);
	};
	if (!isOpen) return null;

	return (
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>

					<div className="qadpt-title"> {(selectedTemplate !== "Banner" || selectedTemplateTour !== "Banner") ? "Overlay" : "Shadow"}</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>

				{/* Status Section */}
				<div
					className="qadpt-controls"
				//style={{ opacity: "0.5", height: "60px" }}
				>
					<Box className="qadpt-control-box">
						<div
							className="qadpt-control-label"

						>
							{selectedTemplate === "Banner" || selectedTemplateTour === "Banner" ? "Enable Shadow" : "Enable Overlay"}
						</div>
						<div>
							<label className="toggle-switch ">
								<input
									type="checkbox"
									checked={status}
									onChange={handleStatusChange}
									name="toggleSwitch"
									disabled={selectedTemplate === "Tour"}
								/>
								<span className="slider"></span>
							</label>
						</div>

					</Box>
					{/* Enable Shadow or Enable Overlay */}
					{/* <div
						style={{
							display: "flex",
							alignItems: "center",
							justifyContent: "space-between",
							padding: "5px",
							borderRadius: "12px",
							backgroundColor: "var(--back-light-color)",
						}}
					>
						<div className="qadpt-label">{selectedTemplate === "Banner" || selectedTemplateTour === "Banner" ? "Enable Shadow" : "Enable Overlay"}</div>
						<label className="toggle-switch ">
    <input
        type="checkbox"
        checked={status}
        onChange={handleStatusChange}
        name="toggleSwitch"
    />
    <span className="slider"></span>
</label>

					</div> */}

					{(selectedTemplate !== "Banner" && selectedTemplateTour !== "Banner") && !status && (
						<>
							<Box className="qadpt-control-box">
								{/* Interact with Page */}
								<div
									className="qadpt-control-label"
								>
									Interact with Page
								</div>
								<div>
									<label className="toggle-switch qadpt-toggle-group">
										<input
											type="checkbox"
											checked={interaction}
											onChange={handleInteractionChange}
											name="toggleSwitch"
											disabled={selectedTemplate === "Tour"}
										/>
										<span className="slider"></span>
									</label>
								</div>
							</Box>
						</>
					)}
				</div>

				<div className="qadpt-drawerFooter">
					<Button
						variant="contained"
						onClick={handleApplyChanges}
						className={`qadpt-btn`}
					>
						Apply
					</Button>
				</div>
			</div>
		</div>
	);
};

export default OverlaySettings;
