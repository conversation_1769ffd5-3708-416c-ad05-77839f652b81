import React from "react";
import { Box, Typography, Dialog, DialogActions, IconButton, Button, DialogTitle, DialogContent,DialogContentText } from "@mui/material";
import WarningIcon from "@mui/icons-material/Warning";
const AlertPopup = ({
   
    openWarning,
    setopenWarning,
    handleLeave,
}: {
  openWarning: boolean; setopenWarning: (params: boolean) => void; handleLeave: () => void;
    }) => { 
    

    return (
			<Dialog
				sx={{
					zIndex: 999999,				}}
				open={openWarning}
				onClose={() => setopenWarning(false)}
				PaperProps={{
					style: {
						borderRadius: "4px",
						maxWidth: "400px",
						textAlign: "center",
						maxHeight: "300px",
						boxShadow: "none",
					},
				}}
			>
				<DialogTitle sx={{ padding: 0 }}>
					<div style={{ display: "flex", justifyContent: "center", padding: "10px" }}>
						<div
							style={{
								backgroundColor: "#e4b6b0",
								borderRadius: "50%",
								width: "40px",
								height: "40px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
							<WarningIcon sx={{ color: "#F44336", height: "20px", width: "20px" }} />
						</div>
					</div>
					{/* <Typography sx={{ fontSize: "16px !important", fontWeight: 600, padding: "0 10px" }}>
                Delete {GuideTypetoDelete}
            </Typography> */}
				</DialogTitle>

				<DialogContent sx={{ padding: "20px !important" }}>
					<DialogContentText style={{ fontSize: "14px", color: "#000" }}>
						You Will Loose Changes, If you Leave With Out Saving
					</DialogContentText>
				</DialogContent>

				<DialogActions sx={{ justifyContent: "space-between", borderTop: "1px solid var(--border-color)" }}>
					<Button
						onClick={() => setopenWarning(false)}
						sx={{
							color: "#9E9E9E",
							border: "1px solid #9E9E9E",
							borderRadius: "8px",
							textTransform: "capitalize",
							padding: "var(--button-padding)",
							lineHeight: "var(--button-lineheight)",
						}}
					>
						Go Back
					</Button>
					<Button
						onClick={handleLeave}
						sx={{
							backgroundColor: "var(--error-color)",
							color: "#FFF",
							borderRadius: "8px",
							textTransform: "capitalize",
							padding: "var(--button-padding)",
							lineHeight: "var(--button-lineheight)",
							// "&:hover": {
							// 	backgroundColor: "#D32F2F",
							// },
						}}
					>
						Close
					</Button>
				</DialogActions>
			</Dialog>
		);
}
export default AlertPopup;