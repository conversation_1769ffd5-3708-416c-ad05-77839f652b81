import React from "react";
import { Box, TextField } from "@mui/material";

type TTextFieldProps = import("@mui/material").TextFieldProps & {};
const CustomTextField = (props: TTextFieldProps) => {
	const { placeholder, onChange, value } = props;
	return (
		<TextField
			{...props}
			placeholder={placeholder}
			size="small"
			fullWidth
			value={value}
			onChange={onChange}
			InputProps={{
				className: "qadpt-input-field",
				disableUnderline: true,
			}}
			variant="standard"
		/>
	);
};

export default CustomTextField;
