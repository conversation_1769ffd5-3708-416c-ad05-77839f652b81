import React, { useState } from 'react';
import { Box, Typography, Popover, IconButton } from '@mui/material';
import RemoveIcon from '@mui/icons-material/Remove';
import AddIcon from '@mui/icons-material/Add';
import { uploadfile, hyperlink, uploadicon, replaceimageicon, backgroundcoloricon, copyicon, deleteicon, sectionheight } from  '../../../assets/icons/icons';

const VideoSection: React.FC = () => {
  const [videoSrc, setVideoSrc] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [videoHeight, setVideoHeight] = useState<number>(335);
  const [showSection, setShowSection] = useState<boolean>(true);

  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    padding: 0,
    margin: 0,
    overflow: 'hidden',
  };

  const videoContainerStyle: React.CSSProperties = {
    width: '100%',
    height: `${videoHeight}px`,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
    margin: 0,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  };

  const videoStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    margin: 0,
    padding: 0,
    borderRadius: '0',
  };

  const iconRowStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    gap: '16px',
  };

  const iconTextStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    width: '100%',
  };

  const handleVideoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setVideoSrc(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'video-popover' : undefined;

  const handleIncreaseHeight = () => {
    setVideoHeight((prevHeight) => prevHeight + 10);
  };

  const handleDecreaseHeight = () => {
    setVideoHeight((prevHeight) => (prevHeight > 10 ? prevHeight - 10 : prevHeight));
  };

  const triggerVideoUpload = () => {
    document.getElementById('replace-video-upload')?.click();
  };

  // Function to delete the section
  const handleDeleteSection = () => {
    setShowSection(false); // Hide the section by updating the state
  };

  return (
    <>
      {showSection && (
        <Box sx={containerStyle}>
          <Box sx={videoContainerStyle} onClick={handleClick}>
            {videoSrc ? (
              <video src={videoSrc} controls style={videoStyle} />
            ) : (
              <Box sx={{ textAlign: 'center', width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                <Box sx={iconTextStyle}>
                  <span
                    dangerouslySetInnerHTML={{ __html: uploadfile }}
                    style={{ fontSize: '48px', display: 'inline-block' }}
                  />
                  <Typography variant="h6" align="center">
                    Upload Video
                  </Typography>
                </Box>

                <Typography variant="body2" align="center" color="textSecondary">
                  Drag & Drop to upload video
                </Typography>

                <Typography variant="body2" align="center" color="textSecondary" sx={{ marginTop: '8px' }}>
                  Or
                </Typography>

                <Box sx={iconRowStyle}>
                  <span
                    dangerouslySetInnerHTML={{ __html: hyperlink }}
                    style={{ color: 'black', cursor: 'pointer', fontSize: '32px' }}
                  />
                  <span
                    onClick={() => document.getElementById('video-file-upload')?.click()}
                    dangerouslySetInnerHTML={{ __html: uploadicon }}
                    style={{ color: 'black', cursor: 'pointer', fontSize: '32px' }}
                  />
                  <input
                    type="file"
                    id="video-file-upload"
                    style={{ display: 'none' }}
                    accept="video/*"
                    onChange={handleVideoUpload}
                  />
                </Box>
              </Box>
            )}
          </Box>
          <Popover
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            PaperProps={{
              style: {
                height: '44px',
                width: '500px',
                marginLeft: 'auto',
                marginRight: 'auto',
                marginTop: '-10px',
              },
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '100%', paddingLeft: '10px', paddingRight: '10px', fontSize: '12px' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>
                <span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />
                <Typography fontSize="12px" onClick={triggerVideoUpload}>
                  Replace Video
                </Typography>
                <input
                  type="file"
                  id="replace-video-upload"
                  style={{ display: 'none' }}
                  accept="video/*"
                  onChange={handleVideoUpload}
                />
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>
                <span dangerouslySetInnerHTML={{ __html: sectionheight }} />
                <IconButton onClick={handleDecreaseHeight} size="small">
                  <RemoveIcon fontSize="small" />
                </IconButton>
                <Typography fontSize="12px">{videoHeight}</Typography>
                <IconButton onClick={handleIncreaseHeight} size="small">
                  <AddIcon fontSize="small" />
                </IconButton>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>
                <span dangerouslySetInnerHTML={{ __html: backgroundcoloricon }} />
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px' }}>
                <span dangerouslySetInnerHTML={{ __html: copyicon }} />
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '12px', cursor: 'pointer' }} onClick={handleDeleteSection}>
                <span dangerouslySetInnerHTML={{ __html: deleteicon }} />
              </Box>
            </Box>
          </Popover>
        </Box>
      )}
    </>
  );
};

export default VideoSection;