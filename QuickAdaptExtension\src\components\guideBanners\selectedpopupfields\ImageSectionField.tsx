import React, { useState } from "react";
import { <PERSON>, <PERSON>po<PERSON>, <PERSON>over, IconButton, Dialog, TextField, MenuItem, Button } from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import SettingsIcon from "@mui/icons-material/Settings";
import {
	uploadfile,
	hyperlink,
	files,
	uploadicon,
	replaceimageicon,
	galleryicon,
	backgroundcoloricon,
	copyicon,
	deleteicon,
	sectionheight,
} from "../../../assets/icons/icons";
import ImageGalleryPopup from "./ImageGalleryPopup";
import ImageProperties from "./ImageProperties";
//import ImagePageInteractions from "./PageInteraction";
import "../guideBanner.css";
const ImageSectionField: React.FC<{ setImageSrc: any; imageSrc: any; setImageName: any }> = ({
	setImageSrc,
	imageSrc,
	setImageName,
}) => {
	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
	const [imageHeight, setImageHeight] = useState<number>(70);
	const [showSection, setShowSection] = useState<boolean>(true);
	const [isGalleryOpen, setIsGalleryOpen] = useState(false);
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);

	const containerStyle: React.CSSProperties = {
		width: "100%",
		display: "flex",
		flexDirection: "row",
		justifyContent: "flex-start",
		alignItems: "center",
		padding: 0,
		margin: 0,
		overflow: "hidden",
	};

	const imageContainerStyle: React.CSSProperties = {
		width: "100%",
		height: `${imageHeight}px`,
		display: "flex",
		justifyContent: "center",
		alignItems: "center",
		padding: 0,
		margin: 0,
		overflow: "hidden",
		backgroundColor: "#f0f0f0",
	};

	const imageStyle: React.CSSProperties = {
		width: "100%",
		height: "100%",
		objectFit: "cover",
		margin: 0,
		padding: 0,
		borderRadius: "0",
	};

	const iconRowStyle: React.CSSProperties = {
		display: "flex",
		justifyContent: "center",
		gap: "16px",
	};

	const iconTextStyle: React.CSSProperties = {
		display: "flex",
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
		gap: "8px",
		width: "100%",
	};

	// const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
	// 	const file = event.target.files?.[0];
	// 	if (file) {
	// 		const reader = new FileReader();
	// 		reader.onloadend = () => {
	// 			// Use reader.result directly, including the base64 prefix
	// 			const base64String = reader.result as string;
	// 			setImageSrc(base64String); // Set the full base64 string including the prefix
	// 			setImageName(file.name);
	// 		};
	// 		reader.readAsDataURL(file); // This will automatically include the correct prefix
	// 	}
	// };
	const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];

		if (file) {
			setImageName(event.target.files?.[0].name);
			const reader = new FileReader();
			reader.onloadend = () => {
				setImageSrc(reader.result as string);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleClick = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	const open = Boolean(anchorEl);
	const id = open ? "image-popover" : undefined;

	const handleIncreaseHeight = () => {
		setImageHeight((prevHeight) => prevHeight + 5);
	};

	const handleDecreaseHeight = () => {
		setImageHeight((prevHeight) => (prevHeight > 10 ? prevHeight - 5 : prevHeight));
	};

	const triggerImageUpload = () => {
		document.getElementById("replace-upload")?.click();
	};

	// Function to delete the section
	const handleDeleteSection = () => {
		setShowSection(false); // Hide the section by updating the state
	};
	const handleOpenGallery = () => {
		setIsGalleryOpen(true);
	};

	const handleCloseGallery = () => {
		setIsGalleryOpen(false);
	};

	const handleOpenSettings = () => {
		setIsSettingsOpen(true);
	};

	const handleCloseSettings = () => {
		setIsSettingsOpen(false);
	};

	return (
		<>
			{showSection && (
				<Box sx={containerStyle}>
					<Box className="qadpt-imageupload" onClick={handleClick}>
  {imageSrc ? (
    <img src={imageSrc} alt="Uploaded" />
  ) : (
    <Box className="upload-container">
      <Box className="icon-text">
        <span
          dangerouslySetInnerHTML={{ __html: uploadfile }}
        />
        <Typography align="center">
          Upload file
        </Typography>
      </Box>

      <Box className="icon-row">
        <span
          dangerouslySetInnerHTML={{ __html: hyperlink }}
        />
        <span
          dangerouslySetInnerHTML={{ __html: files }}
        />
        <span
          onClick={() => document.getElementById("file-upload")?.click()}
          dangerouslySetInnerHTML={{ __html: uploadicon }}
        />
        <input
          type="file"
          id="file-upload"
          accept="image/*"
          onChange={handleImageUpload}
        />
      </Box>
    </Box>
  )}
</Box>

<Popover
	id={id}
	open={open}
	anchorEl={anchorEl}
	onClose={handleClose}
	anchorOrigin={{
		vertical: "bottom",
		horizontal: "center",
	}}
	transformOrigin={{
		vertical: "bottom",
		horizontal: "center",
	}}
	PaperProps={{
		className: "qadpt-imagepopup",
	}}
>
	<Box className="qadpt-imagepopup-content">
		<Box className="qadpt-imagepopup-item">
			<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />
			<Typography className="qadpt-imagepopup-text" onClick={triggerImageUpload}>
				Replace Image
			</Typography>
			<input
				type="file"
				id="replace-upload"
				className="qadpt-imagepopup-upload"
				accept="image/*"
				onChange={handleImageUpload}
			/>
		</Box>

		<Box className="qadpt-imagepopup-item">
			<span dangerouslySetInnerHTML={{ __html: galleryicon }} />
			<Typography className="qadpt-imagepopup-text" onClick={handleOpenGallery}>
				Open Gallery
			</Typography>
		</Box>

		<Box className="qadpt-imagepopup-item">
			<span dangerouslySetInnerHTML={{ __html: sectionheight }} />
			<IconButton onClick={handleDecreaseHeight} size="small">
				<RemoveIcon fontSize="small" />
			</IconButton>
			<Typography className="qadpt-imagepopup-text">{imageHeight}</Typography>
			<IconButton onClick={handleIncreaseHeight} size="small">
				<AddIcon fontSize="small" />
			</IconButton>
		</Box>

		<Box className="qadpt-imagepopup-item">
			<SettingsIcon fontSize="small" onClick={handleOpenSettings} />
		</Box>

		<Box className="qadpt-imagepopup-item">
			<span dangerouslySetInnerHTML={{ __html: copyicon }} />
		</Box>

		<Box className="qadpt-imagepopup-item" onClick={handleDeleteSection}>
			<span dangerouslySetInnerHTML={{ __html: deleteicon }} />
		</Box>
	</Box>
</Popover>

					{/* Settings Dialog */}
					<Dialog
						open={isSettingsOpen}
						onClose={handleCloseSettings}
					>
						<ImageProperties></ImageProperties>

						{/* <Box sx={{ padding: "20px", minWidth: "300px" }}>
							<Typography variant="h6">Settings</Typography>
							{/* Additional Settings Fields 
							<TextField
								label="Hyperlink"
								fullWidth
								sx={{ marginTop: "10px" }}
							/>
							<TextField
								label="Actions"
								fullWidth
								select
								sx={{ marginTop: "10px" }}
							>
								<MenuItem value="Action1">Action1</MenuItem>
								<MenuItem value="Action2">Action2</MenuItem>
							</TextField>
							<TextField
								label="Formatting"
								fullWidth
								select
								sx={{ marginTop: "10px" }}
							>
								<MenuItem value="Format1">Format1</MenuItem>
								<MenuItem value="Format2">Format2</MenuItem>
							</TextField>
							<Box sx={{ marginTop: "20px", display: "flex", justifyContent: "flex-end" }}>
								<Button onClick={handleCloseSettings}>Close</Button>
							</Box>
						</Box> */}
					</Dialog>

					{isGalleryOpen && (
						<ImageGalleryPopup
							open={isGalleryOpen}
							onClose={handleCloseGallery}
						/>
					)}
				</Box>
			)}
		</>
	);
};

export default ImageSectionField;
